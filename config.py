"""
تكوين التطبيق المحسن
Enhanced Application Configuration
"""
import os
import secrets
from datetime import timedel<PERSON>
from typing import Dict, Any

# استيراد اختياري لـ pydantic
try:
    from pydantic_settings import BaseSettings
    from pydantic import Field, validator
    PYDANTIC_AVAILABLE = True
except ImportError:
    PYDANTIC_AVAILABLE = False

    # إنشاء بدائل بسيطة
    class BaseSettings:
        pass

    def Field(**kwargs):
        return kwargs.get('default')


class Config:
    """إعدادات التطبيق الأساسية"""

    # Application Settings
    SECRET_KEY = os.environ.get('SECRET_KEY') or secrets.token_hex(32)
    DEBUG = os.environ.get('DEBUG', 'False').lower() == 'true'
    TESTING = os.environ.get('TESTING', 'False').lower() == 'true'

    # Database Configuration
    DATABASE_URL = os.environ.get('DATABASE_URL', 'sqlite:///employee_management.db')
    DATABASE_POOL_SIZE = int(os.environ.get('DATABASE_POOL_SIZE', 10))
    DATABASE_POOL_TIMEOUT = int(os.environ.get('DATABASE_POOL_TIMEOUT', 20))
    DATABASE_POOL_RECYCLE = int(os.environ.get('DATABASE_POOL_RECYCLE', 3600))

    # Security Settings
    SECURITY_PASSWORD_SALT = os.environ.get('SECURITY_PASSWORD_SALT') or secrets.token_hex(16)
    SECURITY_REGISTERABLE = os.environ.get('SECURITY_REGISTERABLE', 'False').lower() == 'true'
    SECURITY_RECOVERABLE = os.environ.get('SECURITY_RECOVERABLE', 'True').lower() == 'true'
    SECURITY_TRACKABLE = os.environ.get('SECURITY_TRACKABLE', 'True').lower() == 'true'
    SECURITY_CHANGEABLE = os.environ.get('SECURITY_CHANGEABLE', 'True').lower() == 'true'
    SECURITY_CONFIRMABLE = os.environ.get('SECURITY_CONFIRMABLE', 'False').lower() == 'true'

    # JWT Settings
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY') or secrets.token_hex(32)
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=int(os.environ.get('JWT_ACCESS_TOKEN_HOURS', 1)))
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=int(os.environ.get('JWT_REFRESH_TOKEN_DAYS', 30)))

    # Session Configuration
    PERMANENT_SESSION_LIFETIME = timedelta(hours=int(os.environ.get('SESSION_LIFETIME_HOURS', 2)))
    SESSION_COOKIE_SECURE = os.environ.get('SESSION_COOKIE_SECURE', 'True').lower() == 'true'
    SESSION_COOKIE_HTTPONLY = os.environ.get('SESSION_COOKIE_HTTPONLY', 'True').lower() == 'true'
    SESSION_COOKIE_SAMESITE = os.environ.get('SESSION_COOKIE_SAMESITE', 'Lax')

    # File Upload Settings
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER', 'uploads')
    MAX_CONTENT_LENGTH = int(os.environ.get('MAX_CONTENT_LENGTH', 16 * 1024 * 1024))  # 16MB
    ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif', 'txt'}

    # Email Configuration
    MAIL_SERVER = os.environ.get('MAIL_SERVER', 'localhost')
    MAIL_PORT = int(os.environ.get('MAIL_PORT', 587))
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'True').lower() == 'true'
    MAIL_USE_SSL = os.environ.get('MAIL_USE_SSL', 'False').lower() == 'true'
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME', '')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD', '')
    MAIL_DEFAULT_SENDER = os.environ.get('MAIL_DEFAULT_SENDER', '<EMAIL>')

    # Redis Configuration
    REDIS_URL = os.environ.get('REDIS_URL', 'redis://localhost:6379/0')
    CACHE_TYPE = os.environ.get('CACHE_TYPE', 'redis')
    CACHE_REDIS_URL = os.environ.get('CACHE_REDIS_URL', 'redis://localhost:6379/1')
    CACHE_DEFAULT_TIMEOUT = int(os.environ.get('CACHE_DEFAULT_TIMEOUT', 300))

    # Rate Limiting
    RATELIMIT_STORAGE_URL = os.environ.get('RATELIMIT_STORAGE_URL', 'redis://localhost:6379/2')
    RATELIMIT_DEFAULT = os.environ.get('RATELIMIT_DEFAULT', '100 per hour')

    # Celery Configuration
    CELERY_BROKER_URL = os.environ.get('CELERY_BROKER_URL', 'redis://localhost:6379/3')
    CELERY_RESULT_BACKEND = os.environ.get('CELERY_RESULT_BACKEND', 'redis://localhost:6379/4')

    # Logging Configuration
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FILE = os.environ.get('LOG_FILE', 'logs/employee_management.log')
    LOG_MAX_BYTES = int(os.environ.get('LOG_MAX_BYTES', 10485760))  # 10MB
    LOG_BACKUP_COUNT = int(os.environ.get('LOG_BACKUP_COUNT', 5))

    # Monitoring & Analytics
    SENTRY_DSN = os.environ.get('SENTRY_DSN', '')
    ANALYTICS_ENABLED = os.environ.get('ANALYTICS_ENABLED', 'True').lower() == 'true'

    # API Configuration
    API_TITLE = os.environ.get('API_TITLE', 'Employee Management API')
    API_VERSION = os.environ.get('API_VERSION', 'v1')
    API_DESCRIPTION = os.environ.get('API_DESCRIPTION', 'نظام إدارة الموظفين الشامل')

    # Internationalization
    LANGUAGES = {
        'ar': 'العربية',
        'en': 'English'
    }
    BABEL_DEFAULT_LOCALE = os.environ.get('BABEL_DEFAULT_LOCALE', 'ar')
    BABEL_DEFAULT_TIMEZONE = os.environ.get('BABEL_DEFAULT_TIMEZONE', 'Asia/Riyadh')

    # Security Headers
    FORCE_HTTPS = os.environ.get('FORCE_HTTPS', 'True').lower() == 'true'
    CONTENT_SECURITY_POLICY = {
        'default-src': "'self'",
        'script-src': "'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com",
        'style-src': "'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com",
        'img-src': "'self' data: https:",
        'font-src': "'self' https://cdnjs.cloudflare.com",
        'connect-src': "'self'",
        'frame-ancestors': "'none'"
    }

    # Application Features
    ENABLE_2FA = os.environ.get('ENABLE_2FA', 'True').lower() == 'true'
    ENABLE_EMAIL_NOTIFICATIONS = os.environ.get('ENABLE_EMAIL_NOTIFICATIONS', 'True').lower() == 'true'
    ENABLE_SMS_NOTIFICATIONS = os.environ.get('ENABLE_SMS_NOTIFICATIONS', 'False').lower() == 'true'
    ENABLE_PUSH_NOTIFICATIONS = os.environ.get('ENABLE_PUSH_NOTIFICATIONS', 'True').lower() == 'true'
    ENABLE_AUDIT_LOG = os.environ.get('ENABLE_AUDIT_LOG', 'True').lower() == 'true'
    ENABLE_ANALYTICS = os.environ.get('ENABLE_ANALYTICS', 'True').lower() == 'true'

    # Business Rules
    DEFAULT_ANNUAL_LEAVE_DAYS = int(os.environ.get('DEFAULT_ANNUAL_LEAVE_DAYS', 30))
    DEFAULT_SICK_LEAVE_DAYS = int(os.environ.get('DEFAULT_SICK_LEAVE_DAYS', 15))
    MAX_CONSECUTIVE_LEAVE_DAYS = int(os.environ.get('MAX_CONSECUTIVE_LEAVE_DAYS', 14))
    MIN_ADVANCE_NOTICE_DAYS = int(os.environ.get('MIN_ADVANCE_NOTICE_DAYS', 3))


class DevelopmentConfig(Config):
    """إعدادات بيئة التطوير"""
    DEBUG = True
    TESTING = False
    SESSION_COOKIE_SECURE = False
    FORCE_HTTPS = False
    DATABASE_URL = os.environ.get('DATABASE_URL', 'sqlite:///employee_management_dev.db')


class TestingConfig(Config):
    """إعدادات بيئة الاختبار"""
    DEBUG = True
    TESTING = True
    DATABASE_URL = "sqlite:///:memory:"
    WTF_CSRF_ENABLED = False
    SESSION_COOKIE_SECURE = False
    FORCE_HTTPS = False
    ENABLE_EMAIL_NOTIFICATIONS = False
    ENABLE_SMS_NOTIFICATIONS = False


class ProductionConfig(Config):
    """إعدادات بيئة الإنتاج"""
    DEBUG = False
    TESTING = False
    SESSION_COOKIE_SECURE = True
    FORCE_HTTPS = True


# Configuration mapping
config_map = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}


def get_config(config_name=None):
    """الحصول على إعدادات التطبيق"""
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')

    config_class = config_map.get(config_name, DevelopmentConfig)
    return config_class


# تحميل متغيرات البيئة من ملف .env إذا كان موجوداً
def load_env_file():
    """تحميل متغيرات البيئة من ملف .env"""
    env_file = os.path.join(os.path.dirname(__file__), '.env')

    if os.path.exists(env_file):
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip().strip('"').strip("'")

                    # تعيين متغير البيئة إذا لم يكن موجوداً
                    if key not in os.environ:
                        os.environ[key] = value


# تحميل متغيرات البيئة عند استيراد الملف
load_env_file()
