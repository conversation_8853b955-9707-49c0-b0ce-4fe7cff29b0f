"""
اختبارات شاملة للميزات المحسنة في ALEMIS
Comprehensive Tests for Enhanced ALEMIS Features
"""
import pytest
import json
import tempfile
import os
from datetime import datetime, date, timedelta
from unittest.mock import Mock, patch, MagicMock

# استيراد الوحدات المطلوبة للاختبار
try:
    from app import create_app
    from app.security_enhanced import SecurityManager
    from app.reports_enhanced import EnhancedReportsManager, ReportType, ReportFilter
    from app.cache import CacheManager
    APP_AVAILABLE = True
except ImportError:
    APP_AVAILABLE = False


class TestSecurityManager:
    """اختبارات مدير الأمان المحسن"""
    
    @pytest.fixture
    def security_manager(self):
        """إنشاء مثيل مدير الأمان للاختبار"""
        return SecurityManager()
    
    def test_password_hashing(self, security_manager):
        """اختبار تشفير كلمات المرور"""
        password = "test_password_123"
        hashed = security_manager.hash_password(password)
        
        assert hashed is not None
        assert hashed != password
        assert security_manager.verify_password(password, hashed)
        assert not security_manager.verify_password("wrong_password", hashed)
    
    def test_password_strength_requirements(self, security_manager):
        """اختبار متطلبات قوة كلمة المرور"""
        weak_passwords = ["123", "password", "abc"]
        strong_passwords = ["MyStr0ng!Pass", "Complex@123", "Secure#Pass2024"]
        
        for weak_pass in weak_passwords:
            # يجب أن تفشل كلمات المرور الضعيفة
            assert len(weak_pass) < 8  # مثال على فحص بسيط
        
        for strong_pass in strong_passwords:
            # يجب أن تنجح كلمات المرور القوية
            assert len(strong_pass) >= 8
            hashed = security_manager.hash_password(strong_pass)
            assert security_manager.verify_password(strong_pass, hashed)
    
    def test_data_encryption(self, security_manager):
        """اختبار تشفير البيانات"""
        sensitive_data = "معلومات حساسة للاختبار"
        
        # محاولة التشفير
        encrypted = security_manager.encrypt_data(sensitive_data)
        
        if encrypted and encrypted != sensitive_data:
            # إذا تم التشفير، اختبر فك التشفير
            decrypted = security_manager.decrypt_data(encrypted)
            assert decrypted == sensitive_data
        else:
            # إذا لم يتم التشفير (مكتبة غير متاحة)، تأكد من إرجاع البيانات كما هي
            assert encrypted == sensitive_data
    
    def test_csrf_token_generation(self, security_manager):
        """اختبار إنتاج رموز CSRF"""
        with patch('flask.session', {}):
            token1 = security_manager.generate_csrf_token()
            token2 = security_manager.generate_csrf_token()
            
            assert token1 is not None
            assert len(token1) > 0
            # يجب أن يكون نفس الرمز في نفس الجلسة
            assert token1 == token2
    
    def test_2fa_secret_generation(self, security_manager):
        """اختبار إنتاج سر المصادقة الثنائية"""
        user_email = "<EMAIL>"
        result = security_manager.generate_2fa_secret(user_email)
        
        if result:  # إذا كانت المكتبة متاحة
            assert 'secret' in result
            assert 'qr_code' in result
            assert 'uri' in result
            assert len(result['secret']) > 0
        else:
            # إذا لم تكن المكتبة متاحة
            assert result is None


class TestReportsManager:
    """اختبارات مدير التقارير المحسن"""
    
    @pytest.fixture
    def reports_manager(self):
        """إنشاء مثيل مدير التقارير للاختبار"""
        return EnhancedReportsManager()
    
    @pytest.fixture
    def sample_filters(self):
        """مرشحات تجريبية للاختبار"""
        return ReportFilter(
            start_date=date(2024, 1, 1),
            end_date=date(2024, 12, 31),
            department_ids=[1, 2],
            user_ids=[1, 2, 3],
            leave_types=["إجازة اعتيادية", "إجازة مرضية"],
            status="approved"
        )
    
    def test_leave_summary_report(self, reports_manager, sample_filters):
        """اختبار تقرير ملخص الإجازات"""
        report = reports_manager.generate_report(ReportType.LEAVE_SUMMARY, sample_filters)
        
        assert report is not None
        assert 'title' in report
        assert 'data' in report
        assert 'generated_at' in report
        assert report['title'] == "ملخص الإجازات"
        
        # التحقق من بنية البيانات
        if report['data']:
            first_item = report['data'][0]
            expected_keys = ['leave_type', 'total_requests', 'approved_requests', 
                           'rejected_requests', 'pending_requests']
            for key in expected_keys:
                assert key in first_item
    
    def test_employee_performance_report(self, reports_manager, sample_filters):
        """اختبار تقرير أداء الموظفين"""
        report = reports_manager.generate_report(ReportType.EMPLOYEE_PERFORMANCE, sample_filters)
        
        assert report is not None
        assert 'title' in report
        assert 'data' in report
        assert report['title'] == "تقرير أداء الموظفين"
    
    def test_department_analytics_report(self, reports_manager, sample_filters):
        """اختبار تقرير تحليلات الأقسام"""
        report = reports_manager.generate_report(ReportType.DEPARTMENT_ANALYTICS, sample_filters)
        
        assert report is not None
        assert 'title' in report
        assert 'data' in report
        assert report['title'] == "تحليلات الأقسام"
    
    def test_monthly_overview_report(self, reports_manager, sample_filters):
        """اختبار التقرير الشهري"""
        report = reports_manager.generate_report(ReportType.MONTHLY_OVERVIEW, sample_filters)
        
        assert report is not None
        assert 'title' in report
        assert 'data' in report
        assert 'trends' in report
    
    def test_invalid_report_type(self, reports_manager, sample_filters):
        """اختبار نوع تقرير غير صحيح"""
        # محاولة إنتاج تقرير بنوع غير موجود
        with patch.object(reports_manager, 'generate_report') as mock_generate:
            mock_generate.return_value = {"error": "نوع التقرير غير مدعوم"}
            
            result = mock_generate("invalid_type", sample_filters)
            assert "error" in result


class TestCacheManager:
    """اختبارات مدير التخزين المؤقت"""
    
    @pytest.fixture
    def cache_manager(self):
        """إنشاء مثيل مدير التخزين المؤقت للاختبار"""
        return CacheManager()
    
    def test_cache_initialization(self, cache_manager):
        """اختبار تهيئة التخزين المؤقت"""
        # التحقق من أن المدير تم إنشاؤه بنجاح
        assert cache_manager is not None
        
        # محاولة تهيئة مع تطبيق وهمي
        mock_app = Mock()
        mock_app.config = {
            'CACHE_TYPE': 'simple',
            'CACHE_DEFAULT_TIMEOUT': 300
        }
        
        cache_manager.init_app(mock_app)
        assert cache_manager.app == mock_app


class TestDatabaseOptimizations:
    """اختبارات تحسينات قاعدة البيانات"""
    
    def test_sql_file_exists(self):
        """اختبار وجود ملف تحسينات قاعدة البيانات"""
        sql_file_path = "database_optimizations.sql"
        assert os.path.exists(sql_file_path)
        
        # قراءة الملف والتحقق من المحتوى
        with open(sql_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # التحقق من وجود الفهارس المهمة
        assert "CREATE INDEX" in content
        assert "idx_users_username" in content
        assert "idx_leave_requests_user_id" in content
        
        # التحقق من وجود Views
        assert "CREATE VIEW" in content
        assert "employee_summary" in content
        
        # التحقق من وجود Triggers
        assert "CREATE TRIGGER" in content


class TestUIEnhancements:
    """اختبارات تحسينات واجهة المستخدم"""
    
    def test_css_files_exist(self):
        """اختبار وجود ملفات CSS المحسنة"""
        css_files = [
            "static/css/enhanced-ui.css",
            "static/css/style.css"
        ]
        
        for css_file in css_files:
            assert os.path.exists(css_file), f"ملف CSS غير موجود: {css_file}"
    
    def test_js_files_exist(self):
        """اختبار وجود ملفات JavaScript المحسنة"""
        js_files = [
            "static/js/enhanced-ui.js"
        ]
        
        for js_file in js_files:
            assert os.path.exists(js_file), f"ملف JavaScript غير موجود: {js_file}"
    
    def test_css_variables(self):
        """اختبار متغيرات CSS"""
        css_file_path = "static/css/enhanced-ui.css"
        
        with open(css_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # التحقق من وجود متغيرات CSS المهمة
        assert "--primary-500" in content
        assert "--secondary-500" in content
        assert "--success-500" in content
        assert "--radius-lg" in content


class TestRequirements:
    """اختبارات ملفات المتطلبات"""
    
    def test_requirements_files_exist(self):
        """اختبار وجود ملفات المتطلبات"""
        req_files = [
            "requirements.txt",
            "requirements-minimal.txt",
            "requirements-enhanced.txt"
        ]
        
        for req_file in req_files:
            assert os.path.exists(req_file), f"ملف المتطلبات غير موجود: {req_file}"
    
    def test_requirements_content(self):
        """اختبار محتوى ملفات المتطلبات"""
        with open("requirements.txt", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # التحقق من وجود المكتبات الأساسية
        essential_packages = [
            "flask",
            "SQLAlchemy",
            "bcrypt",
            "gunicorn"
        ]
        
        for package in essential_packages:
            assert package.lower() in content.lower(), f"المكتبة مفقودة: {package}"


class TestIntegration:
    """اختبارات التكامل"""
    
    @pytest.fixture
    def app(self):
        """إنشاء تطبيق للاختبار"""
        if not APP_AVAILABLE:
            pytest.skip("التطبيق غير متاح للاختبار")
        
        app = create_app('testing')
        app.config['TESTING'] = True
        app.config['WTF_CSRF_ENABLED'] = False
        
        with app.app_context():
            yield app
    
    @pytest.fixture
    def client(self, app):
        """إنشاء عميل اختبار"""
        return app.test_client()
    
    def test_health_endpoint(self, client):
        """اختبار نقطة فحص الصحة"""
        response = client.get('/health')
        
        if response.status_code == 200:
            data = json.loads(response.data)
            assert 'status' in data
            assert 'timestamp' in data
            assert 'version' in data
            assert 'components' in data
    
    def test_login_page_loads(self, client):
        """اختبار تحميل صفحة تسجيل الدخول"""
        response = client.get('/login')
        
        # يجب أن تعيد الصفحة أو تعيد توجيه
        assert response.status_code in [200, 302]


# إعدادات pytest
def pytest_configure(config):
    """إعداد pytest"""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )


# تشغيل الاختبارات
if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
