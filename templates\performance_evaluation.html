{% extends "base.html" %}

{% block title %}نظام إدارة الموظفين - تقييم الأداء{% endblock %}

{% block styles %}
<style>
    .performance-header {
        background: linear-gradient(135deg, var(--accent-purple) 0%, var(--primary-blue) 50%, var(--secondary-teal) 100%);
        color: var(--white);
        padding: var(--space-2xl) 0;
        margin-bottom: var(--space-2xl);
        border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);
        position: relative;
        overflow: hidden;
    }

    .performance-header::before {
        content: '';
        position: absolute;
        top: -20%;
        right: -5%;
        width: 250px;
        height: 250px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='rgba(255,255,255,0.1)'%3E%3Cpath d='M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'/%3E%3C/svg%3E") no-repeat center;
        background-size: contain;
        animation: performanceFloat 15s ease-in-out infinite;
    }

    .evaluation-tabs {
        display: flex;
        background: var(--white);
        border-radius: var(--radius-xl);
        box-shadow: var(--shadow-sm);
        margin-bottom: var(--space-2xl);
        overflow: hidden;
    }

    .tab-button {
        flex: 1;
        padding: var(--space-lg) var(--space-xl);
        background: var(--off-white);
        border: none;
        font-size: var(--font-size-base);
        font-weight: var(--font-weight-semibold);
        color: var(--dark-gray);
        cursor: pointer;
        transition: var(--transition-normal);
        position: relative;
    }

    .tab-button.active {
        background: var(--primary-blue);
        color: var(--white);
    }

    .tab-button:hover:not(.active) {
        background: var(--light-gray);
        color: var(--near-black);
    }

    .evaluation-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--space-xl);
        margin-bottom: var(--space-2xl);
    }

    .evaluation-card {
        background: var(--white);
        border: var(--border-light);
        border-radius: var(--radius-2xl);
        box-shadow: var(--shadow-sm);
        padding: var(--space-xl);
        transition: var(--transition-normal);
        position: relative;
        overflow: hidden;
    }

    .evaluation-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(90deg, var(--card-color, var(--primary-blue)), var(--card-color-light, var(--primary-blue-light)));
    }

    .evaluation-card:hover {
        transform: translateY(-8px);
        box-shadow: var(--shadow-xl);
    }

    .evaluation-card.individual { --card-color: var(--primary-blue); --card-color-light: var(--primary-blue-light); }
    .evaluation-card.department { --card-color: var(--accent-green); --card-color-light: var(--success); }
    .evaluation-card.annual { --card-color: var(--warning); --card-color-light: var(--accent-orange); }
    .evaluation-card.quarterly { --card-color: var(--accent-purple); --card-color-light: #8b5cf6; }

    .card-header {
        display: flex;
        align-items: center;
        gap: var(--space-lg);
        margin-bottom: var(--space-lg);
    }

    .card-icon {
        width: 60px;
        height: 60px;
        border-radius: var(--radius-xl);
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, var(--card-color, var(--primary-blue)), var(--card-color-light, var(--primary-blue-light)));
        color: var(--white);
        font-size: var(--font-size-2xl);
    }

    .card-title {
        font-size: var(--font-size-xl);
        font-weight: var(--font-weight-bold);
        color: var(--near-black);
        margin-bottom: var(--space-xs);
    }

    .card-subtitle {
        font-size: var(--font-size-sm);
        color: var(--dark-gray);
    }

    .kpi-metrics {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--space-lg);
        margin-bottom: var(--space-2xl);
    }

    .metric-card {
        background: var(--white);
        border: var(--border-light);
        border-radius: var(--radius-xl);
        box-shadow: var(--shadow-sm);
        padding: var(--space-lg);
        text-align: center;
        transition: var(--transition-normal);
        position: relative;
        overflow: hidden;
    }

    .metric-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--metric-color, var(--primary-blue));
    }

    .metric-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-md);
    }

    .metric-card.productivity { --metric-color: var(--accent-green); }
    .metric-card.quality { --metric-color: var(--primary-blue); }
    .metric-card.teamwork { --metric-color: var(--secondary-teal); }
    .metric-card.innovation { --metric-color: var(--accent-purple); }
    .metric-card.leadership { --metric-color: var(--warning); }
    .metric-card.attendance { --metric-color: var(--accent-red); }

    .metric-value {
        font-size: var(--font-size-3xl);
        font-weight: var(--font-weight-bold);
        color: var(--metric-color, var(--primary-blue));
        margin-bottom: var(--space-sm);
    }

    .metric-label {
        font-size: var(--font-size-base);
        font-weight: var(--font-weight-semibold);
        color: var(--near-black);
        margin-bottom: var(--space-xs);
    }

    .metric-change {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--space-xs);
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
    }

    .change-positive {
        color: var(--success);
    }

    .change-negative {
        color: var(--danger);
    }

    .evaluation-form {
        background: var(--white);
        border: var(--border-light);
        border-radius: var(--radius-2xl);
        box-shadow: var(--shadow-sm);
        padding: var(--space-2xl);
        margin-bottom: var(--space-2xl);
    }

    .form-section {
        margin-bottom: var(--space-2xl);
    }

    .section-title {
        font-size: var(--font-size-xl);
        font-weight: var(--font-weight-bold);
        color: var(--near-black);
        margin-bottom: var(--space-lg);
        padding-bottom: var(--space-md);
        border-bottom: 2px solid var(--light-gray);
    }

    .rating-group {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: var(--space-lg);
        align-items: center;
        padding: var(--space-lg);
        border: var(--border-light);
        border-radius: var(--radius-lg);
        margin-bottom: var(--space-md);
        transition: var(--transition-fast);
    }

    .rating-group:hover {
        background: var(--off-white);
        border-color: var(--primary-blue);
    }

    .rating-label {
        font-size: var(--font-size-base);
        font-weight: var(--font-weight-medium);
        color: var(--near-black);
    }

    .rating-stars {
        display: flex;
        gap: var(--space-xs);
        justify-content: flex-end;
    }

    .star {
        width: 30px;
        height: 30px;
        cursor: pointer;
        color: var(--medium-gray);
        font-size: var(--font-size-lg);
        transition: var(--transition-fast);
    }

    .star:hover,
    .star.active {
        color: var(--warning);
        transform: scale(1.1);
    }

    .progress-chart {
        background: var(--white);
        border: var(--border-light);
        border-radius: var(--radius-2xl);
        box-shadow: var(--shadow-sm);
        padding: var(--space-xl);
        margin-bottom: var(--space-2xl);
    }

    .chart-title {
        font-size: var(--font-size-xl);
        font-weight: var(--font-weight-bold);
        color: var(--near-black);
        margin-bottom: var(--space-lg);
        text-align: center;
    }

    .progress-bars {
        display: flex;
        flex-direction: column;
        gap: var(--space-lg);
    }

    .progress-item {
        display: flex;
        align-items: center;
        gap: var(--space-lg);
    }

    .progress-label {
        min-width: 120px;
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
        color: var(--dark-gray);
    }

    .progress-bar {
        flex: 1;
        height: 12px;
        background: var(--light-gray);
        border-radius: var(--radius-full);
        overflow: hidden;
        position: relative;
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, var(--progress-color, var(--primary-blue)), var(--progress-color-light, var(--primary-blue-light)));
        border-radius: var(--radius-full);
        transition: width 1s ease-in-out;
        position: relative;
    }

    .progress-fill::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        animation: shimmer 2s infinite;
    }

    .progress-value {
        min-width: 50px;
        text-align: right;
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-bold);
        color: var(--progress-color, var(--primary-blue));
    }

    @keyframes performanceFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        25% { transform: translateY(-20px) rotate(5deg); }
        50% { transform: translateY(-10px) rotate(-5deg); }
        75% { transform: translateY(-25px) rotate(3deg); }
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    /* التصميم المتجاوب */
    @media (max-width: 1200px) {
        .evaluation-grid {
            grid-template-columns: 1fr;
        }
    }

    @media (max-width: 768px) {
        .evaluation-tabs {
            flex-direction: column;
        }

        .kpi-metrics {
            grid-template-columns: 1fr;
        }

        .rating-group {
            grid-template-columns: 1fr;
            text-align: center;
        }

        .rating-stars {
            justify-content: center;
        }

        .progress-item {
            flex-direction: column;
            align-items: flex-start;
            gap: var(--space-sm);
        }

        .progress-label {
            min-width: auto;
        }

        .progress-bar {
            width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-professional">
    <!-- رأس صفحة التقييم -->
    <div class="performance-header">
        <div class="container-professional">
            <div class="text-center">
                <div class="dashboard-icon">
                    <i class="fas fa-star"></i>
                </div>
                <h1 class="dashboard-title">نظام تقييم الأداء</h1>
                <p class="dashboard-subtitle">تقييم شامل للأداء الفردي والجماعي مع مؤشرات KPI متقدمة</p>
            </div>
        </div>
    </div>

    <!-- تبويبات التقييم -->
    <div class="evaluation-tabs">
        <button class="tab-button active" data-tab="individual">
            <i class="fas fa-user me-2"></i>
            التقييم الفردي
        </button>
        <button class="tab-button" data-tab="department">
            <i class="fas fa-users me-2"></i>
            تقييم الأقسام
        </button>
        <button class="tab-button" data-tab="annual">
            <i class="fas fa-calendar-alt me-2"></i>
            التقييم السنوي
        </button>
        <button class="tab-button" data-tab="quarterly">
            <i class="fas fa-chart-line me-2"></i>
            التقييم الربعي
        </button>
    </div>

    <!-- مؤشرات الأداء الرئيسية -->
    <div class="kpi-metrics">
        <div class="metric-card productivity">
            <div class="metric-value">87%</div>
            <div class="metric-label">الإنتاجية</div>
            <div class="metric-change change-positive">
                <i class="fas fa-arrow-up"></i>
                +5.2%
            </div>
        </div>

        <div class="metric-card quality">
            <div class="metric-value">92%</div>
            <div class="metric-label">جودة العمل</div>
            <div class="metric-change change-positive">
                <i class="fas fa-arrow-up"></i>
                +3.1%
            </div>
        </div>

        <div class="metric-card teamwork">
            <div class="metric-value">89%</div>
            <div class="metric-label">العمل الجماعي</div>
            <div class="metric-change change-positive">
                <i class="fas fa-arrow-up"></i>
                +2.8%
            </div>
        </div>

        <div class="metric-card innovation">
            <div class="metric-value">76%</div>
            <div class="metric-label">الابتكار</div>
            <div class="metric-change change-negative">
                <i class="fas fa-arrow-down"></i>
                -1.5%
            </div>
        </div>

        <div class="metric-card leadership">
            <div class="metric-value">84%</div>
            <div class="metric-label">القيادة</div>
            <div class="metric-change change-positive">
                <i class="fas fa-arrow-up"></i>
                +4.3%
            </div>
        </div>

        <div class="metric-card attendance">
            <div class="metric-value">95%</div>
            <div class="metric-label">الحضور</div>
            <div class="metric-change change-positive">
                <i class="fas fa-arrow-up"></i>
                +1.2%
            </div>
        </div>
    </div>

    <!-- شبكة بطاقات التقييم -->
    <div class="evaluation-grid">
        <div class="evaluation-card individual">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-user-check"></i>
                </div>
                <div>
                    <div class="card-title">التقييم الفردي</div>
                    <div class="card-subtitle">تقييم أداء الموظفين الفردي</div>
                </div>
            </div>
            <p>نظام شامل لتقييم أداء كل موظف على حدة مع معايير محددة ومؤشرات قابلة للقياس.</p>
            <button class="btn-professional btn-primary mt-3">
                <i class="fas fa-plus me-2"></i>
                إضافة تقييم جديد
            </button>
        </div>

        <div class="evaluation-card department">
            <div class="card-header">
                <div class="card-icon">
                    <i class="fas fa-building"></i>
                </div>
                <div>
                    <div class="card-title">تقييم الأقسام</div>
                    <div class="card-subtitle">تقييم أداء الأقسام المختلفة</div>
                </div>
            </div>
            <p>تحليل شامل لأداء كل قسم مع مقارنات وإحصائيات تفصيلية لتحسين الكفاءة.</p>
            <button class="btn-professional btn-success mt-3">
                <i class="fas fa-chart-bar me-2"></i>
                عرض التقارير
            </button>
        </div>
    </div>

    <!-- مخطط التقدم -->
    <div class="progress-chart">
        <h3 class="chart-title">مؤشرات الأداء الشهرية</h3>
        <div class="progress-bars">
            <div class="progress-item">
                <div class="progress-label">الإنتاجية</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 87%; --progress-color: var(--accent-green); --progress-color-light: var(--success);"></div>
                </div>
                <div class="progress-value" style="--progress-color: var(--accent-green);">87%</div>
            </div>

            <div class="progress-item">
                <div class="progress-label">جودة العمل</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 92%; --progress-color: var(--primary-blue); --progress-color-light: var(--primary-blue-light);"></div>
                </div>
                <div class="progress-value" style="--progress-color: var(--primary-blue);">92%</div>
            </div>

            <div class="progress-item">
                <div class="progress-label">العمل الجماعي</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 89%; --progress-color: var(--secondary-teal); --progress-color-light: var(--info);"></div>
                </div>
                <div class="progress-value" style="--progress-color: var(--secondary-teal);">89%</div>
            </div>

            <div class="progress-item">
                <div class="progress-label">الابتكار</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 76%; --progress-color: var(--accent-purple); --progress-color-light: #8b5cf6;"></div>
                </div>
                <div class="progress-value" style="--progress-color: var(--accent-purple);">76%</div>
            </div>

            <div class="progress-item">
                <div class="progress-label">القيادة</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 84%; --progress-color: var(--warning); --progress-color-light: var(--accent-orange);"></div>
                </div>
                <div class="progress-value" style="--progress-color: var(--warning);">84%</div>
            </div>

            <div class="progress-item">
                <div class="progress-label">الحضور</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 95%; --progress-color: var(--accent-red); --progress-color-light: var(--danger);"></div>
                </div>
                <div class="progress-value" style="--progress-color: var(--accent-red);">95%</div>
            </div>
        </div>
    </div>
</div>

<script>
// تفعيل التبويبات
document.querySelectorAll('.tab-button').forEach(button => {
    button.addEventListener('click', function() {
        // إزالة الفئة النشطة من جميع التبويبات
        document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
        // إضافة الفئة النشطة للتبويب المضغوط
        this.classList.add('active');
        
        const tabType = this.dataset.tab;
        console.log('تم تغيير التبويب إلى:', tabType);
        
        // هنا يمكن إضافة منطق تحديث المحتوى حسب التبويب
    });
});

// تفعيل نظام التقييم بالنجوم
document.querySelectorAll('.star').forEach(star => {
    star.addEventListener('click', function() {
        const rating = parseInt(this.dataset.rating);
        const group = this.closest('.rating-stars');
        
        // إزالة الفئة النشطة من جميع النجوم في المجموعة
        group.querySelectorAll('.star').forEach(s => s.classList.remove('active'));
        
        // إضافة الفئة النشطة للنجوم حتى النجمة المضغوطة
        for (let i = 1; i <= rating; i++) {
            group.querySelector(`[data-rating="${i}"]`).classList.add('active');
        }
        
        console.log('تم تحديد التقييم:', rating);
    });
});

// تحديث أشرطة التقدم عند تحميل الصفحة
window.addEventListener('load', function() {
    const progressBars = document.querySelectorAll('.progress-fill');
    progressBars.forEach(bar => {
        const width = bar.style.width;
        bar.style.width = '0%';
        setTimeout(() => {
            bar.style.width = width;
        }, 500);
    });
});
</script>
{% endblock %}
