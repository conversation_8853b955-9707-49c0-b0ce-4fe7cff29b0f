<svg width="120" height="120" viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- التدرجات الاحترافية -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0066cc;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#17a2b8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#28a745;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f8f9fa;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fd7e14;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#dc3545;stop-opacity:1" />
    </linearGradient>
    
    <!-- الظلال -->
    <filter id="dropShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="rgba(0,0,0,0.2)"/>
    </filter>
    
    <filter id="innerShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feOffset dx="0" dy="2"/>
      <feGaussianBlur stdDeviation="2" result="offset-blur"/>
      <feFlood flood-color="rgba(0,0,0,0.1)"/>
      <feComposite in2="offset-blur" operator="in"/>
    </filter>
  </defs>
  
  <!-- الخلفية الدائرية -->
  <circle cx="60" cy="60" r="55" fill="url(#backgroundGradient)" stroke="url(#primaryGradient)" stroke-width="3" filter="url(#dropShadow)"/>
  
  <!-- الدائرة الداخلية -->
  <circle cx="60" cy="60" r="45" fill="none" stroke="url(#primaryGradient)" stroke-width="2" opacity="0.3"/>
  
  <!-- رمز الصليب الطبي المحسن -->
  <g transform="translate(60,60)">
    <!-- الصليب الرئيسي -->
    <rect x="-3" y="-20" width="6" height="40" fill="url(#primaryGradient)" rx="3" filter="url(#innerShadow)"/>
    <rect x="-20" y="-3" width="40" height="6" fill="url(#primaryGradient)" rx="3" filter="url(#innerShadow)"/>
    
    <!-- النقاط الزخرفية -->
    <circle cx="0" cy="0" r="4" fill="url(#accentGradient)"/>
    <circle cx="0" cy="-15" r="2" fill="url(#accentGradient)" opacity="0.8"/>
    <circle cx="0" cy="15" r="2" fill="url(#accentGradient)" opacity="0.8"/>
    <circle cx="-15" cy="0" r="2" fill="url(#accentGradient)" opacity="0.8"/>
    <circle cx="15" cy="0" r="2" fill="url(#accentGradient)" opacity="0.8"/>
  </g>
  
  <!-- رموز إضافية للمختبر والإدارة -->
  <g transform="translate(30,30)" opacity="0.6">
    <!-- رمز المجهر -->
    <rect x="0" y="8" width="2" height="6" fill="url(#primaryGradient)"/>
    <circle cx="1" cy="6" r="3" fill="none" stroke="url(#primaryGradient)" stroke-width="1"/>
    <rect x="-1" y="14" width="4" height="1" fill="url(#primaryGradient)"/>
  </g>
  
  <g transform="translate(90,30)" opacity="0.6">
    <!-- رمز الملف الطبي -->
    <rect x="0" y="0" width="8" height="10" fill="none" stroke="url(#primaryGradient)" stroke-width="1" rx="1"/>
    <line x1="2" y1="3" x2="6" y2="3" stroke="url(#primaryGradient)" stroke-width="0.5"/>
    <line x1="2" y1="5" x2="6" y2="5" stroke="url(#primaryGradient)" stroke-width="0.5"/>
    <line x1="2" y1="7" x2="4" y2="7" stroke="url(#primaryGradient)" stroke-width="0.5"/>
  </g>
  
  <g transform="translate(30,90)" opacity="0.6">
    <!-- رمز الإحصائيات -->
    <rect x="0" y="6" width="2" height="4" fill="url(#primaryGradient)"/>
    <rect x="3" y="4" width="2" height="6" fill="url(#primaryGradient)"/>
    <rect x="6" y="2" width="2" height="8" fill="url(#primaryGradient)"/>
  </g>
  
  <g transform="translate(90,90)" opacity="0.6">
    <!-- رمز الفريق -->
    <circle cx="2" cy="2" r="1.5" fill="url(#primaryGradient)"/>
    <circle cx="6" cy="2" r="1.5" fill="url(#primaryGradient)"/>
    <path d="M0,8 Q2,6 4,8 Q6,6 8,8" fill="none" stroke="url(#primaryGradient)" stroke-width="1"/>
  </g>
  
  <!-- النص التوضيحي -->
  <text x="60" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" font-weight="bold" fill="url(#primaryGradient)">EMS</text>
</svg>
