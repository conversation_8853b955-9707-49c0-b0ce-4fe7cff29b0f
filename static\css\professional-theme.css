/* ===== نظام إدارة الموظفين - النمط الاحترافي ===== */
/* Employee Management System - Professional Theme */

/* ===== متغيرات الألوان الاحترافية ===== */
:root {
    /* الألوان الأساسية - Professional Color Palette */
    --white: #ffffff;
    --off-white: #fafbfc;
    --light-gray: #f8f9fa;
    --medium-gray: #e9ecef;
    --dark-gray: #6c757d;
    --charcoal: #495057;
    --near-black: #343a40;
    --black: #212529;

    /* ألوان التمييز - Accent Colors */
    --primary-blue: #0066cc;
    --primary-blue-light: #3385d6;
    --primary-blue-dark: #004d99;
    --secondary-teal: #17a2b8;
    --accent-green: #28a745;
    --accent-orange: #fd7e14;
    --accent-red: #dc3545;
    --accent-purple: #6f42c1;

    /* ألوان الحالة - Status Colors */
    --success: #28a745;
    --success-light: #d4edda;
    --warning: #ffc107;
    --warning-light: #fff3cd;
    --danger: #dc3545;
    --danger-light: #f8d7da;
    --info: #17a2b8;
    --info-light: #d1ecf1;

    /* الظلال والحدود - Shadows & Borders */
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    --shadow-xl: 0 2rem 4rem rgba(0, 0, 0, 0.2);
    
    --border-light: 1px solid var(--medium-gray);
    --border-medium: 2px solid var(--dark-gray);
    --border-accent: 2px solid var(--primary-blue);

    /* نصف الأقطار - Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-full: 50%;

    /* المسافات - Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    --space-3xl: 4rem;

    /* الخطوط - Typography */
    --font-family-primary: 'Tajawal', 'Cairo', 'Segoe UI', system-ui, sans-serif;
    --font-family-secondary: 'Inter', 'Roboto', sans-serif;
    
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;

    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;

    /* الانتقالات - Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;

    /* الشبكة - Grid & Layout */
    --container-max-width: 1200px;
    --sidebar-width: 280px;
    --header-height: 70px;
}

/* ===== إعادة تعيين الأساسيات ===== */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

*::before,
*::after {
    box-sizing: border-box;
}

/* ===== الأنماط الأساسية ===== */
html {
    font-size: 16px;
    scroll-behavior: smooth;
    height: 100%;
}

body {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: 1.6;
    color: var(--near-black);
    background-color: var(--off-white);
    direction: rtl;
    text-align: right;
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ===== التدرجات الخلفية الاحترافية ===== */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
}

.bg-gradient-light {
    background: linear-gradient(135deg, var(--white) 0%, var(--light-gray) 100%);
}

.bg-gradient-professional {
    background: linear-gradient(135deg, var(--off-white) 0%, var(--light-gray) 50%, var(--medium-gray) 100%);
}

/* ===== الخطوط والنصوص ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: var(--font-weight-bold);
    line-height: 1.2;
    margin-bottom: var(--space-md);
    color: var(--near-black);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
    margin-bottom: var(--space-md);
    color: var(--charcoal);
}

.text-muted {
    color: var(--dark-gray) !important;
}

.text-primary {
    color: var(--primary-blue) !important;
}

.text-success {
    color: var(--success) !important;
}

.text-warning {
    color: var(--warning) !important;
}

.text-danger {
    color: var(--danger) !important;
}

/* ===== الروابط ===== */
a {
    color: var(--primary-blue);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--primary-blue-dark);
    text-decoration: underline;
}

a:focus {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}

/* ===== البطاقات الاحترافية ===== */
.card-professional {
    background: var(--white);
    border: var(--border-light);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    overflow: hidden;
}

.card-professional:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-professional .card-header {
    background: var(--light-gray);
    border-bottom: var(--border-light);
    padding: var(--space-lg);
    font-weight: var(--font-weight-semibold);
}

.card-professional .card-body {
    padding: var(--space-lg);
}

.card-professional .card-footer {
    background: var(--light-gray);
    border-top: var(--border-light);
    padding: var(--space-md) var(--space-lg);
}

/* ===== الأزرار الاحترافية ===== */
.btn-professional {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-sm) var(--space-lg);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    line-height: 1.5;
    border: 2px solid transparent;
    border-radius: var(--radius-md);
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-fast);
    min-height: 44px;
    gap: var(--space-sm);
}

.btn-professional:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.25);
}

.btn-primary {
    background-color: var(--primary-blue);
    border-color: var(--primary-blue);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--primary-blue-dark);
    border-color: var(--primary-blue-dark);
    color: var(--white);
}

.btn-secondary {
    background-color: var(--dark-gray);
    border-color: var(--dark-gray);
    color: var(--white);
}

.btn-secondary:hover {
    background-color: var(--charcoal);
    border-color: var(--charcoal);
    color: var(--white);
}

.btn-outline-primary {
    background-color: transparent;
    border-color: var(--primary-blue);
    color: var(--primary-blue);
}

.btn-outline-primary:hover {
    background-color: var(--primary-blue);
    border-color: var(--primary-blue);
    color: var(--white);
}

/* ===== النماذج الاحترافية ===== */
.form-control-professional {
    display: block;
    width: 100%;
    padding: var(--space-sm) var(--space-md);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: 1.5;
    color: var(--near-black);
    background-color: var(--white);
    border: var(--border-light);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.form-control-professional:focus {
    border-color: var(--primary-blue);
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
}

.form-label-professional {
    display: inline-block;
    margin-bottom: var(--space-sm);
    font-weight: var(--font-weight-medium);
    color: var(--near-black);
}

/* ===== الجداول الاحترافية ===== */
.table-professional {
    width: 100%;
    margin-bottom: var(--space-lg);
    background-color: var(--white);
    border-collapse: collapse;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table-professional th,
.table-professional td {
    padding: var(--space-md);
    text-align: right;
    border-bottom: 1px solid var(--medium-gray);
}

.table-professional th {
    background-color: var(--light-gray);
    font-weight: var(--font-weight-semibold);
    color: var(--near-black);
}

.table-professional tbody tr:hover {
    background-color: var(--off-white);
}
