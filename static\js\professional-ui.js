/**
 * نظام إدارة الموظفين - واجهة المستخدم الاحترافية
 * Employee Management System - Professional UI
 */

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeProfessionalUI();
});

/**
 * تهيئة واجهة المستخدم الاحترافية
 */
function initializeProfessionalUI() {
    // تهيئة البطاقات التفاعلية
    initializeInteractiveCards();
    
    // تهيئة الأزرار المتقدمة
    initializeAdvancedButtons();
    
    // تهيئة النماذج المتقدمة
    initializeAdvancedForms();
    
    // تهيئة القوائم المنسدلة
    initializeDropdowns();
    
    // تهيئة التبويبات
    initializeTabs();
    
    // تهيئة مفاتيح التبديل
    initializeSwitches();
    
    // تهيئة الرسوم المتحركة
    initializeAnimations();
    
    // تهيئة التصميم المتجاوب
    initializeResponsiveFeatures();
}

/**
 * تهيئة البطاقات التفاعلية
 */
function initializeInteractiveCards() {
    const cards = document.querySelectorAll('.interactive-card, .stats-card');
    
    cards.forEach(card => {
        // إضافة تأثير النقر
        card.addEventListener('click', function(e) {
            if (!e.target.closest('a, button, input, select, textarea')) {
                this.classList.add('clicked');
                setTimeout(() => {
                    this.classList.remove('clicked');
                }, 200);
            }
        });
        
        // إضافة تأثير التمرير
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
}

/**
 * تهيئة الأزرار المتقدمة
 */
function initializeAdvancedButtons() {
    const buttons = document.querySelectorAll('.btn-advanced, .btn-gradient-primary, .btn-gradient-success, .btn-gradient-warning, .btn-gradient-danger');
    
    buttons.forEach(button => {
        // إضافة تأثير الموجة عند النقر
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple-effect');
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
}

/**
 * تهيئة النماذج المتقدمة
 */
function initializeAdvancedForms() {
    // تهيئة الحقول العائمة
    const floatingLabels = document.querySelectorAll('.floating-label');
    
    floatingLabels.forEach(container => {
        const input = container.querySelector('.form-control-advanced');
        const label = container.querySelector('.form-label-advanced');
        
        if (input && label) {
            // التحقق من القيمة الأولية
            if (input.value) {
                label.classList.add('active');
            }
            
            input.addEventListener('focus', () => {
                label.classList.add('active');
            });
            
            input.addEventListener('blur', () => {
                if (!input.value) {
                    label.classList.remove('active');
                }
            });
        }
    });
    
    // تحسين التحقق من صحة النماذج
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const requiredFields = this.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                showNotification('يرجى ملء جميع الحقول المطلوبة', 'warning');
            }
        });
    });
}

/**
 * تهيئة القوائم المنسدلة
 */
function initializeDropdowns() {
    const dropdowns = document.querySelectorAll('.dropdown-professional');
    
    dropdowns.forEach(dropdown => {
        const toggle = dropdown.querySelector('.nav-link-professional, .dropdown-toggle');
        const menu = dropdown.querySelector('.dropdown-menu-professional');
        
        if (toggle && menu) {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                
                // إغلاق القوائم الأخرى
                dropdowns.forEach(otherDropdown => {
                    if (otherDropdown !== dropdown) {
                        otherDropdown.classList.remove('show');
                    }
                });
                
                dropdown.classList.toggle('show');
            });
        }
    });
    
    // إغلاق القوائم عند النقر خارجها
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.dropdown-professional')) {
            dropdowns.forEach(dropdown => {
                dropdown.classList.remove('show');
            });
        }
    });
}

/**
 * تهيئة التبويبات
 */
function initializeTabs() {
    const tabLinks = document.querySelectorAll('.tab-link');
    
    tabLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href').substring(1);
            const targetPane = document.getElementById(targetId);
            
            if (targetPane) {
                // إزالة الفئة النشطة من جميع التبويبات والألواح
                document.querySelectorAll('.tab-link').forEach(l => l.classList.remove('active'));
                document.querySelectorAll('.tab-pane').forEach(p => p.classList.remove('active'));
                
                // إضافة الفئة النشطة للتبويب والوحة الحالية
                this.classList.add('active');
                targetPane.classList.add('active');
            }
        });
    });
}

/**
 * تهيئة مفاتيح التبديل
 */
function initializeSwitches() {
    const switches = document.querySelectorAll('.switch-advanced input');
    
    switches.forEach(switchInput => {
        switchInput.addEventListener('change', function() {
            const event = new CustomEvent('switchToggle', {
                detail: {
                    checked: this.checked,
                    value: this.value
                }
            });
            this.dispatchEvent(event);
        });
    });
}

/**
 * تهيئة الرسوم المتحركة
 */
function initializeAnimations() {
    // تهيئة مراقب التقاطع للرسوم المتحركة
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);
    
    // مراقبة العناصر القابلة للرسوم المتحركة
    const animatedElements = document.querySelectorAll('.stats-card, .interactive-card, .card-professional');
    animatedElements.forEach(el => observer.observe(el));
}

/**
 * تهيئة التصميم المتجاوب
 */
function initializeResponsiveFeatures() {
    // تحسين القوائم للأجهزة المحمولة
    const isMobile = window.innerWidth <= 768;
    
    if (isMobile) {
        // تحسين اللمس للأجهزة المحمولة
        document.body.classList.add('mobile-device');
        
        // تحسين القوائم المنسدلة للأجهزة المحمولة
        const dropdowns = document.querySelectorAll('.dropdown-professional');
        dropdowns.forEach(dropdown => {
            dropdown.addEventListener('touchstart', function(e) {
                e.stopPropagation();
            });
        });
    }
    
    // مراقبة تغيير حجم النافذة
    window.addEventListener('resize', debounce(() => {
        const newIsMobile = window.innerWidth <= 768;
        
        if (newIsMobile !== isMobile) {
            location.reload(); // إعادة تحميل الصفحة عند تغيير نوع الجهاز
        }
    }, 250));
}

/**
 * عرض الإشعارات
 */
function showNotification(message, type = 'info', duration = 5000) {
    const notification = document.createElement('div');
    notification.className = `alert-professional alert-${type}`;
    notification.innerHTML = `
        <div class="alert-icon">
            <i class="fas fa-${getIconForType(type)}"></i>
        </div>
        <div class="alert-content">
            <div class="alert-message">${message}</div>
        </div>
    `;
    
    // إضافة الإشعار إلى الصفحة
    document.body.appendChild(notification);
    
    // إزالة الإشعار بعد المدة المحددة
    setTimeout(() => {
        notification.remove();
    }, duration);
}

/**
 * الحصول على الأيقونة المناسبة لنوع الإشعار
 */
function getIconForType(type) {
    const icons = {
        success: 'check-circle',
        warning: 'exclamation-triangle',
        danger: 'times-circle',
        info: 'info-circle'
    };
    return icons[type] || 'info-circle';
}

/**
 * دالة التأخير لتحسين الأداء
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * تحسين الأداء - تحميل الصور بشكل كسول
 */
function initializeLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    
    const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
}

// تصدير الدوال للاستخدام العام
window.ProfessionalUI = {
    showNotification,
    initializeProfessionalUI
};
