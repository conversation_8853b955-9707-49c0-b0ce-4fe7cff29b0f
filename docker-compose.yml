version: '3.8'

services:
  # ===== تطبيق ALEMIS الرئيسي =====
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=${FLASK_ENV:-production}
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-here}
      - DATABASE_URL=postgresql://alemis:${POSTGRES_PASSWORD:-alemis123}@postgres:5432/alemis
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis123}@redis:6379/0
      - CACHE_REDIS_URL=redis://:${REDIS_PASSWORD:-redis123}@redis:6379/1
      - RATELIMIT_STORAGE_URL=redis://:${REDIS_PASSWORD:-redis123}@redis:6379/2
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD:-redis123}@redis:6379/3
      - CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD:-redis123}@redis:6379/4
    volumes:
      - .:/app
      - uploads_data:/app/uploads
      - logs_data:/app/logs
      - backups_data:/app/backups
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - alemis_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ===== Celery Worker للمهام الخلفية =====
  celery-worker:
    build:
      context: .
      dockerfile: Dockerfile
    command: celery -A app.celery_app worker --loglevel=info --concurrency=4
    environment:
      - FLASK_ENV=${FLASK_ENV:-production}
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-here}
      - DATABASE_URL=postgresql://alemis:${POSTGRES_PASSWORD:-alemis123}@postgres:5432/alemis
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis123}@redis:6379/0
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD:-redis123}@redis:6379/3
      - CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD:-redis123}@redis:6379/4
    volumes:
      - uploads_data:/app/uploads
      - logs_data:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - alemis_network
    restart: unless-stopped

  # ===== Celery Beat للمهام المجدولة =====
  celery-beat:
    build:
      context: .
      dockerfile: Dockerfile
    command: celery -A app.celery_app beat --loglevel=info
    environment:
      - FLASK_ENV=${FLASK_ENV:-production}
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-here}
      - DATABASE_URL=postgresql://alemis:${POSTGRES_PASSWORD:-alemis123}@postgres:5432/alemis
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis123}@redis:6379/0
      - CELERY_BROKER_URL=redis://:${REDIS_PASSWORD:-redis123}@redis:6379/3
      - CELERY_RESULT_BACKEND=redis://:${REDIS_PASSWORD:-redis123}@redis:6379/4
    volumes:
      - logs_data:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - alemis_network
    restart: unless-stopped

  # ===== Nginx كخادم عكسي =====
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./static:/var/www/static:ro
    depends_on:
      - app
    networks:
      - alemis_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost"]
      interval: 30s
      timeout: 10s
      retries: 3

  # قاعدة بيانات PostgreSQL
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=alemis
      - POSTGRES_USER=alemis
      - POSTGRES_PASSWORD=alemis123
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - alemis_network
    restart: unless-stopped

  # Redis للتخزين المؤقت والجلسات
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - alemis_network
    restart: unless-stopped

  # Celery Worker للمهام الخلفية
  celery_worker:
    build: .
    command: celery -A app.celery worker --loglevel=info
    environment:
      - FLASK_ENV=development
      - DATABASE_URL=*******************************************/alemis
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/3
      - CELERY_RESULT_BACKEND=redis://redis:6379/4
    volumes:
      - .:/app
      - uploads_data:/app/uploads
      - logs_data:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - alemis_network
    restart: unless-stopped

  # Celery Beat للمهام المجدولة
  celery_beat:
    build: .
    command: celery -A app.celery beat --loglevel=info
    environment:
      - FLASK_ENV=development
      - DATABASE_URL=*******************************************/alemis
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/3
      - CELERY_RESULT_BACKEND=redis://redis:6379/4
    volumes:
      - .:/app
      - logs_data:/app/logs
    depends_on:
      - postgres
      - redis
    networks:
      - alemis_network
    restart: unless-stopped



  # Prometheus لمراقبة الأداء
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - alemis_network
    restart: unless-stopped

  # Grafana لعرض المقاييس
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - alemis_network
    restart: unless-stopped

  # Elasticsearch للبحث والتحليل
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - alemis_network
    restart: unless-stopped

  # Kibana لعرض بيانات Elasticsearch
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - alemis_network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  uploads_data:
  logs_data:
  static_files:
  prometheus_data:
  grafana_data:
  elasticsearch_data:

networks:
  alemis_network:
    driver: bridge
