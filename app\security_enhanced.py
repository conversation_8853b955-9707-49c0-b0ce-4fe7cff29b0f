"""
نظام الأمان المحسن لـ ALEMIS
Enhanced Security System for ALEMIS
"""
import os
import secrets
import hashlib
import hmac
import time
from datetime import datetime, timedelta
from functools import wraps
from typing import Optional, Dict, Any, List
import logging

# استيراد اختياري للمكتبات المتقدمة
try:
    from cryptography.fernet import Ferne<PERSON>
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    import bcrypt
    CRYPTO_AVAILABLE = True
except ImportError:
    CRYPTO_AVAILABLE = False

try:
    import pyotp
    import qrcode
    from io import BytesIO
    import base64
    TOTP_AVAILABLE = True
except ImportError:
    TOTP_AVAILABLE = False

try:
    from flask import request, session, current_app, g
    from flask_limiter import Limiter
    from flask_limiter.util import get_remote_address
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False

logger = logging.getLogger(__name__)


class SecurityManager:
    """مدير الأمان المحسن"""
    
    def __init__(self, app=None):
        self.app = app
        self.cipher_suite = None
        self.rate_limiter = None
        self.failed_attempts = {}
        self.blocked_ips = {}
        
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """تهيئة نظام الأمان"""
        self.app = app
        
        # تهيئة التشفير
        if CRYPTO_AVAILABLE:
            self._init_encryption()
        
        # تهيئة Rate Limiting
        if FLASK_AVAILABLE:
            self._init_rate_limiting()
        
        # تسجيل معالجات الطلبات
        self._register_handlers()
    
    def _init_encryption(self):
        """تهيئة نظام التشفير"""
        try:
            encryption_key = self.app.config.get('ENCRYPTION_KEY')
            if not encryption_key:
                # إنتاج مفتاح جديد
                encryption_key = Fernet.generate_key()
                self.app.config['ENCRYPTION_KEY'] = encryption_key
                logger.warning("تم إنتاج مفتاح تشفير جديد. يجب حفظه في متغيرات البيئة.")
            
            if isinstance(encryption_key, str):
                encryption_key = encryption_key.encode()
            
            self.cipher_suite = Fernet(encryption_key)
            logger.info("تم تهيئة نظام التشفير بنجاح")
            
        except Exception as e:
            logger.error(f"فشل في تهيئة التشفير: {e}")
            self.cipher_suite = None
    
    def _init_rate_limiting(self):
        """تهيئة نظام تحديد المعدل"""
        try:
            self.rate_limiter = Limiter(
                app=self.app,
                key_func=get_remote_address,
                default_limits=["200 per day", "50 per hour"],
                storage_uri=self.app.config.get('RATELIMIT_STORAGE_URL', 'memory://')
            )
            logger.info("تم تهيئة نظام تحديد المعدل بنجاح")
            
        except Exception as e:
            logger.error(f"فشل في تهيئة Rate Limiting: {e}")
            self.rate_limiter = None
    
    def _register_handlers(self):
        """تسجيل معالجات الطلبات"""
        if not FLASK_AVAILABLE:
            return
        
        @self.app.before_request
        def security_before_request():
            """معالج الأمان قبل الطلب"""
            # فحص IP المحظور
            if self._is_ip_blocked(request.remote_addr):
                return "IP محظور", 403
            
            # فحص CSRF للطلبات POST
            if request.method == 'POST':
                if not self._verify_csrf_token():
                    return "رمز CSRF غير صحيح", 403
            
            # تسجيل محاولة الوصول
            self._log_access_attempt()
        
        @self.app.after_request
        def security_after_request(response):
            """معالج الأمان بعد الطلب"""
            # إضافة رؤوس الأمان
            self._add_security_headers(response)
            return response
    
    def hash_password(self, password: str) -> str:
        """تشفير كلمة المرور"""
        if CRYPTO_AVAILABLE:
            # استخدام bcrypt للتشفير القوي
            salt = bcrypt.gensalt()
            return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
        else:
            # بديل بسيط باستخدام SHA256 + Salt
            salt = secrets.token_hex(16)
            password_hash = hashlib.sha256((password + salt).encode()).hexdigest()
            return f"{salt}:{password_hash}"
    
    def verify_password(self, password: str, password_hash: str) -> bool:
        """التحقق من كلمة المرور"""
        if CRYPTO_AVAILABLE:
            try:
                return bcrypt.checkpw(password.encode('utf-8'), password_hash.encode('utf-8'))
            except:
                return False
        else:
            # بديل بسيط
            try:
                salt, stored_hash = password_hash.split(':')
                password_hash_check = hashlib.sha256((password + salt).encode()).hexdigest()
                return hmac.compare_digest(stored_hash, password_hash_check)
            except:
                return False
    
    def encrypt_data(self, data: str) -> Optional[str]:
        """تشفير البيانات"""
        if not self.cipher_suite:
            return data
        
        try:
            encrypted_data = self.cipher_suite.encrypt(data.encode())
            return base64.urlsafe_b64encode(encrypted_data).decode()
        except Exception as e:
            logger.error(f"فشل في تشفير البيانات: {e}")
            return None
    
    def decrypt_data(self, encrypted_data: str) -> Optional[str]:
        """فك تشفير البيانات"""
        if not self.cipher_suite:
            return encrypted_data
        
        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = self.cipher_suite.decrypt(encrypted_bytes)
            return decrypted_data.decode()
        except Exception as e:
            logger.error(f"فشل في فك التشفير: {e}")
            return None
    
    def generate_2fa_secret(self, user_email: str) -> Optional[Dict[str, str]]:
        """إنتاج سر المصادقة الثنائية"""
        if not TOTP_AVAILABLE:
            return None
        
        try:
            secret = pyotp.random_base32()
            totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
                name=user_email,
                issuer_name="ALEMIS"
            )
            
            # إنتاج QR Code
            qr = qrcode.QRCode(version=1, box_size=10, border=5)
            qr.add_data(totp_uri)
            qr.make(fit=True)
            
            img = qr.make_image(fill_color="black", back_color="white")
            buffer = BytesIO()
            img.save(buffer, format='PNG')
            qr_code = base64.b64encode(buffer.getvalue()).decode()
            
            return {
                'secret': secret,
                'qr_code': qr_code,
                'uri': totp_uri
            }
        except Exception as e:
            logger.error(f"فشل في إنتاج سر المصادقة الثنائية: {e}")
            return None
    
    def verify_2fa_token(self, secret: str, token: str) -> bool:
        """التحقق من رمز المصادقة الثنائية"""
        if not TOTP_AVAILABLE:
            return True  # تجاهل إذا لم تكن متاحة
        
        try:
            totp = pyotp.TOTP(secret)
            return totp.verify(token, valid_window=1)
        except Exception as e:
            logger.error(f"فشل في التحقق من رمز المصادقة الثنائية: {e}")
            return False
    
    def generate_csrf_token(self) -> str:
        """إنتاج رمز CSRF"""
        if 'csrf_token' not in session:
            session['csrf_token'] = secrets.token_hex(16)
        return session['csrf_token']
    
    def _verify_csrf_token(self) -> bool:
        """التحقق من رمز CSRF"""
        token = request.form.get('csrf_token') or request.headers.get('X-CSRF-Token')
        return token and session.get('csrf_token') == token
    
    def _is_ip_blocked(self, ip: str) -> bool:
        """فحص ما إذا كان IP محظور"""
        if ip in self.blocked_ips:
            block_time = self.blocked_ips[ip]
            if time.time() - block_time < 3600:  # حظر لمدة ساعة
                return True
            else:
                del self.blocked_ips[ip]
        return False
    
    def _log_failed_attempt(self, ip: str):
        """تسجيل محاولة فاشلة"""
        current_time = time.time()
        if ip not in self.failed_attempts:
            self.failed_attempts[ip] = []
        
        # إضافة المحاولة الحالية
        self.failed_attempts[ip].append(current_time)
        
        # إزالة المحاولات القديمة (أكثر من 15 دقيقة)
        self.failed_attempts[ip] = [
            attempt for attempt in self.failed_attempts[ip]
            if current_time - attempt < 900
        ]
        
        # حظر IP إذا تجاوز 5 محاولات في 15 دقيقة
        if len(self.failed_attempts[ip]) >= 5:
            self.blocked_ips[ip] = current_time
            logger.warning(f"تم حظر IP {ip} بسبب محاولات تسجيل دخول متعددة فاشلة")
    
    def _log_access_attempt(self):
        """تسجيل محاولة الوصول"""
        # يمكن تطوير هذا لاحقاً مع نظام التسجيل المتقدم
        pass
    
    def _add_security_headers(self, response):
        """إضافة رؤوس الأمان"""
        security_headers = {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
            'Referrer-Policy': 'strict-origin-when-cross-origin',
            'Content-Security-Policy': self._get_csp_header(),
            'Permissions-Policy': 'geolocation=(), microphone=(), camera=()'
        }
        
        for header, value in security_headers.items():
            response.headers[header] = value
    
    def _get_csp_header(self) -> str:
        """الحصول على رأس Content Security Policy"""
        csp_directives = [
            "default-src 'self'",
            "script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com",
            "style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com",
            "img-src 'self' data: https:",
            "font-src 'self' https://cdnjs.cloudflare.com https://fonts.gstatic.com",
            "connect-src 'self'",
            "frame-ancestors 'none'",
            "base-uri 'self'",
            "form-action 'self'"
        ]
        return "; ".join(csp_directives)


# مُزخرفات الأمان
def require_auth(f):
    """مُزخرف للتحقق من المصادقة"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not FLASK_AVAILABLE:
            return f(*args, **kwargs)
        
        if 'user_id' not in session:
            return {"error": "مطلوب تسجيل الدخول"}, 401
        return f(*args, **kwargs)
    return decorated_function


def require_role(roles):
    """مُزخرف للتحقق من الدور"""
    if isinstance(roles, str):
        roles = [roles]
    
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not FLASK_AVAILABLE:
                return f(*args, **kwargs)
            
            user_role = session.get('role')
            if not user_role or user_role not in roles:
                return {"error": "ليس لديك صلاحية للوصول"}, 403
            return f(*args, **kwargs)
        return decorated_function
    return decorator


def rate_limit(limit: str):
    """مُزخرف لتحديد المعدل"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # سيتم تطبيقه مع Flask-Limiter
            return f(*args, **kwargs)
        return decorated_function
    return decorator


# إنشاء مثيل عام
security_manager = SecurityManager()
