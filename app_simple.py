#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الموظفين - شركة العميس الطبية
نسخة مبسطة وعاملة
"""

import os
import sqlite3
import hashlib
from datetime import datetime
from flask import Flask, render_template, redirect, url_for, flash, request, session, g

# إعداد التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'alemis-medical-2024-secret-key'
app.config['DATABASE'] = 'alemis.db'

# إعداد قاعدة البيانات
def get_db():
    """الحصول على اتصال قاعدة البيانات"""
    db = getattr(g, '_database', None)
    if db is None:
        db = g._database = sqlite3.connect(app.config['DATABASE'])
        db.row_factory = sqlite3.Row
    return db

def query_db(query, args=(), one=False):
    """تنفيذ استعلام قاعدة البيانات"""
    cur = get_db().execute(query, args)
    rv = cur.fetchall()
    cur.close()
    
    if rv:
        if one:
            return dict(rv[0])
        else:
            return [dict(row) for row in rv]
    else:
        return None if one else []

@app.teardown_appcontext
def close_connection(exception):
    """إغلاق اتصال قاعدة البيانات"""
    db = getattr(g, '_database', None)
    if db is not None:
        db.close()

# وظائف المساعدة
def hash_password(password):
    """تشفير كلمة المرور"""
    return hashlib.sha256(password.encode()).hexdigest()

def check_password(hashed_password, password):
    """التحقق من كلمة المرور"""
    return hashed_password == hash_password(password)

def is_authenticated():
    """التحقق من تسجيل الدخول"""
    return 'user_id' in session

def get_current_user():
    """الحصول على المستخدم الحالي"""
    if 'user_id' in session:
        return query_db('SELECT * FROM users WHERE id = ?', [session['user_id']], one=True)
    return None

def init_db():
    """تهيئة قاعدة البيانات"""
    with app.app_context():
        db = get_db()
        with open('schema.sql', 'r', encoding='utf-8') as f:
            db.executescript(f.read())
        db.commit()
        
        # إضافة البيانات الأساسية
        init_basic_data()

def init_basic_data():
    """إضافة البيانات الأساسية"""
    db = get_db()
    
    # إضافة الأقسام
    departments = [
        ('المختبر الكيميائي', 'قسم المختبر الكيميائي'),
        ('المختبر البيولوجي', 'قسم المختبر البيولوجي'),
        ('الإدارة', 'قسم الإدارة'),
        ('الموارد البشرية', 'قسم الموارد البشرية')
    ]
    
    for name, description in departments:
        existing = query_db('SELECT id FROM departments WHERE name = ?', [name], one=True)
        if not existing:
            db.execute('INSERT INTO departments (name, description) VALUES (?, ?)', [name, description])
    
    # إضافة أنواع الإجازات
    leave_types = [
        ('إجازة اعتيادية', 'الإجازة السنوية العادية', 30),
        ('إجازة مرضية', 'إجازة مرضية بتقرير طبي', 15),
        ('إجازة اضطرارية', 'إجازة اضطرارية لظروف طارئة', 7),
        ('بدل يوم تغطية', 'بدل عن يوم تغطية في الإجازة', 0)
    ]
    
    for name, description, default_days in leave_types:
        existing = query_db('SELECT id FROM leave_types WHERE name = ?', [name], one=True)
        if not existing:
            db.execute('INSERT INTO leave_types (name, description, default_days) VALUES (?, ?, ?)', 
                      [name, description, default_days])
    
    # إضافة المستخدمين الأساسيين
    users = [
        ('admin', '<EMAIL>', hash_password('admin123'), 'مدير', 'النظام', 'admin', 3),
        ('hr', '<EMAIL>', hash_password('admin123'), 'مدير', 'الموارد البشرية', 'hr', 4),
        ('gm', '<EMAIL>', hash_password('admin123'), 'المدير', 'العام', 'gm', 3),
        ('lab_manager', '<EMAIL>', hash_password('admin123'), 'مدير', 'المختبر', 'manager', 1),
        ('employee1', '<EMAIL>', hash_password('emp123'), 'موظف', 'أول', 'employee', 1),
        ('employee2', '<EMAIL>', hash_password('emp123'), 'موظف', 'ثاني', 'employee', 2)
    ]
    
    for username, email, password_hash, first_name, last_name, role, dept_id in users:
        existing = query_db('SELECT id FROM users WHERE username = ?', [username], one=True)
        if not existing:
            db.execute('''INSERT INTO users 
                         (username, email, password_hash, first_name, last_name, role, department_id, is_active) 
                         VALUES (?, ?, ?, ?, ?, ?, ?, 1)''', 
                      [username, email, password_hash, first_name, last_name, role, dept_id])
    
    db.commit()
    print("✅ تم إضافة البيانات الأساسية بنجاح")

# المسارات الأساسية
@app.route('/')
def index():
    """الصفحة الرئيسية"""
    if is_authenticated():
        return redirect(url_for('dashboard'))
    return render_template('login.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    """تسجيل الدخول"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        user = query_db('SELECT * FROM users WHERE username = ?', [username], one=True)
        
        if user and check_password(user['password_hash'], password):
            session['user_id'] = user['id']
            session['username'] = user['username']
            session['role'] = user['role']
            
            flash('تم تسجيل الدخول بنجاح', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    """تسجيل الخروج"""
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

@app.route('/dashboard')
def dashboard():
    """لوحة التحكم"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()
    
    try:
        # إحصائيات أساسية
        stats = {
            'total_employees': 0,
            'pending_requests': 0,
            'monthly_requests': 0
        }
        
        # محاولة الحصول على الإحصائيات الأساسية
        result = query_db('SELECT COUNT(*) as count FROM users WHERE role = "employee" AND is_active = 1', one=True)
        if result:
            stats['total_employees'] = result['count']

        result = query_db('SELECT COUNT(*) as count FROM leave_requests WHERE status = "pending"', one=True)
        if result:
            stats['pending_requests'] = result['count']

        result = query_db('''
            SELECT COUNT(*) as count FROM leave_requests
            WHERE strftime('%Y-%m', created_at) = ?
        ''', [datetime.now().strftime('%Y-%m')], one=True)
        if result:
            stats['monthly_requests'] = result['count']

        return render_template('dashboard.html',
                             user=user,
                             stats=stats)
    except Exception as e:
        print(f"خطأ في لوحة التحكم: {e}")
        return render_template('dashboard.html',
                             user=user,
                             stats={'total_employees': 0, 'pending_requests': 0, 'monthly_requests': 0})

# تشغيل التطبيق
if __name__ == '__main__':
    # تهيئة قاعدة البيانات إذا لم تكن موجودة
    if not os.path.exists('alemis.db'):
        init_db()
    
    print("🚀 بدء تشغيل نظام إدارة الموظفين - شركة العميس الطبية")
    print("📍 الرابط: http://localhost:5000")
    print("👤 حسابات التجربة:")
    print("   - المدير: admin / admin123")
    print("   - الموارد البشرية: hr / admin123")
    print("   - المدير العام: gm / admin123")
    print("   - مدير المختبر: lab_manager / admin123")
    print("   - موظف: employee1 / emp123")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
