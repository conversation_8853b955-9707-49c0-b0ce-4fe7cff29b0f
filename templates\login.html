{% extends "base.html" %}

{% block title %}نظام إدارة الموظفين - تسجيل الدخول{% endblock %}

{% block styles %}
<!-- تضمين ملفات CSS الاحترافية -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/professional-theme.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/components.css') }}">

<style>
    /* ===== تخصيص صفحة تسجيل الدخول ===== */
    body {
        background: var(--white);
        min-height: 100vh;
        font-family: var(--font-family-primary);
        overflow-x: hidden;
        position: relative;
    }

    /* ===== خلفية هندسية احترافية ===== */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            linear-gradient(135deg, var(--off-white) 0%, var(--light-gray) 100%),
            url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cg fill='none' stroke='rgba(0,102,204,0.05)' stroke-width='1'%3E%3Cpath d='M50 10 L90 50 L50 90 L10 50 Z'/%3E%3Ccircle cx='50' cy='50' r='30'/%3E%3Cpath d='M20 20 L80 20 L80 80 L20 80 Z'/%3E%3C/g%3E%3C/svg%3E") repeat;
        background-size: 80px 80px;
        z-index: -1;
        animation: backgroundFloat 60s ease-in-out infinite;
    }

    /* ===== شريط علوي ملون ===== */
    body::after {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg,
            var(--primary-blue) 0%,
            var(--secondary-teal) 25%,
            var(--accent-green) 50%,
            var(--accent-orange) 75%,
            var(--primary-blue) 100%);
        z-index: 1000;
        animation: colorShift 8s ease-in-out infinite;
    }

    /* ===== الحاوي الرئيسي ===== */
    .login-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: var(--space-2xl) var(--space-lg);
        position: relative;
        z-index: 1;
    }

    /* ===== بطاقة تسجيل الدخول الاحترافية ===== */
    .login-card {
        background: var(--white);
        border-radius: var(--radius-2xl);
        box-shadow: var(--shadow-xl);
        overflow: hidden;
        position: relative;
        max-width: 480px;
        width: 100%;
        margin: 0 auto;
        transition: var(--transition-normal);
        border: 1px solid var(--medium-gray);
    }

    .login-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    }

    /* ===== شريط علوي متدرج ===== */
    .login-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 6px;
        background: linear-gradient(90deg,
            var(--primary-blue) 0%,
            var(--secondary-teal) 50%,
            var(--accent-green) 100%);
        z-index: 1;
    }

    /* ===== رأس البطاقة ===== */
    .login-header {
        background: var(--white);
        color: var(--near-black);
        padding: var(--space-3xl) var(--space-2xl) var(--space-xl);
        text-align: center;
        position: relative;
        z-index: 2;
        border-bottom: 1px solid var(--light-gray);
    }

    /* ===== الشعار الاحترافي ===== */
    .login-logo {
        width: 90px;
        height: 90px;
        background: linear-gradient(135deg, var(--primary-blue), var(--secondary-teal));
        border-radius: var(--radius-2xl);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto var(--space-lg);
        transition: var(--transition-normal);
        box-shadow: var(--shadow-md);
        position: relative;
        overflow: hidden;
    }

    .login-logo::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
        transform: rotate(45deg);
        transition: var(--transition-slow);
    }

    .login-logo:hover {
        transform: scale(1.05) rotate(5deg);
        box-shadow: var(--shadow-lg);
    }

    .login-logo:hover::before {
        animation: shimmer 1.5s ease-in-out;
    }

    .login-logo svg {
        width: 50px;
        height: 50px;
        color: var(--white);
        z-index: 1;
        position: relative;
    }

    /* ===== العناوين الاحترافية ===== */
    .login-title {
        font-size: var(--font-size-3xl);
        font-weight: var(--font-weight-bold);
        margin-bottom: var(--space-sm);
        color: var(--primary-blue);
        letter-spacing: -0.5px;
    }

    .login-subtitle {
        font-size: var(--font-size-base);
        color: var(--dark-gray);
        font-weight: var(--font-weight-normal);
        line-height: 1.5;
        margin-bottom: 0;
    }

    /* ===== جسم البطاقة ===== */
    .login-body {
        padding: var(--space-2xl);
        background: var(--white);
    }

    /* ===== مجموعة الحقول الاحترافية ===== */
    .form-group-login {
        margin-bottom: var(--space-xl);
        position: relative;
    }

    /* ===== حقول الإدخال الاحترافية ===== */
    .form-control-login {
        width: 100%;
        height: 56px;
        padding: 0 var(--space-lg) 0 3.5rem;
        border: 2px solid var(--medium-gray);
        border-radius: var(--radius-lg);
        font-size: var(--font-size-base);
        background: var(--off-white);
        transition: var(--transition-fast);
        outline: none;
        font-family: var(--font-family-primary);
        color: var(--near-black);
        font-weight: var(--font-weight-medium);
    }

    .form-control-login:focus {
        border-color: var(--primary-blue);
        background: var(--white);
        box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
        transform: translateY(-2px);
    }

    .form-control-login::placeholder {
        color: var(--dark-gray);
        font-weight: var(--font-weight-normal);
    }

    /* ===== أيقونات الحقول الاحترافية ===== */
    .form-icon {
        position: absolute;
        left: var(--space-lg);
        top: 50%;
        transform: translateY(-50%);
        color: var(--dark-gray);
        font-size: var(--font-size-lg);
        z-index: 2;
        transition: var(--transition-fast);
    }

    .form-group-login:focus-within .form-icon {
        color: var(--primary-blue);
        transform: translateY(-50%) scale(1.1);
    }

    /* ===== الرسوم المتحركة ===== */
    @keyframes backgroundFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        25% { transform: translateY(-10px) rotate(1deg); }
        50% { transform: translateY(-5px) rotate(-1deg); }
        75% { transform: translateY(-15px) rotate(0.5deg); }
    }

    @keyframes colorShift {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.8; }
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* ===== تطبيق الرسوم المتحركة ===== */
    .login-card {
        animation: fadeInUp 0.8s ease-out;
    }

    .login-header {
        animation: fadeInUp 0.8s ease-out 0.2s both;
    }

    .login-body {
        animation: fadeInUp 0.8s ease-out 0.4s both;
    }

    /* ===== التصميم المتجاوب ===== */
    @media (max-width: 768px) {
        .login-container {
            padding: var(--space-lg);
        }

        .login-card {
            max-width: 100%;
            margin: 0;
        }

        .login-header {
            padding: var(--space-2xl) var(--space-lg) var(--space-lg);
        }

        .login-body {
            padding: var(--space-lg);
        }

        .login-title {
            font-size: var(--font-size-2xl);
        }

        .form-control-login {
            height: 52px;
            font-size: var(--font-size-sm);
        }
    }

    @media (max-width: 480px) {
        .login-logo {
            width: 70px;
            height: 70px;
        }

        .login-logo svg {
            width: 40px;
            height: 40px;
        }

        .login-title {
            font-size: var(--font-size-xl);
        }

        .login-subtitle {
            font-size: var(--font-size-sm);
        }
    }

    /* ===== زر إظهار/إخفاء كلمة المرور ===== */
    .password-toggle {
        position: absolute;
        right: 1rem;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: #64748b;
        font-size: 1rem;
        cursor: pointer;
        z-index: 2;
        padding: 8px;
        border-radius: 6px;
        transition: all 0.3s ease;
    }

    .password-toggle:hover {
        color: var(--primary-blue);
        background: rgba(14, 165, 233, 0.1);
    }

    /* ===== تسميات الحقول ===== */
    .form-label-login {
        position: absolute;
        left: 3.5rem;
        top: 50%;
        transform: translateY(-50%);
        color: #64748b;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        pointer-events: none;
        background: transparent;
        z-index: 1;
    }

    .form-control-login:focus + .form-label-login,
    .form-control-login:not(:placeholder-shown) + .form-label-login {
        top: -8px;
        left: 1rem;
        font-size: 0.8rem;
        color: var(--primary-blue);
        font-weight: 600;
        background: var(--white);
        padding: 0 0.5rem;
    }

    /* ===== زر تسجيل الدخول ===== */
    .login-btn {
        width: 100%;
        height: 50px;
        background: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-dark));
        border: none;
        border-radius: 12px;
        color: var(--white);
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-top: 1rem;
        position: relative;
        overflow: hidden;
    }

    .login-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(14, 165, 233, 0.3);
    }

    .login-btn:active {
        transform: translateY(0);
    }

    /* ===== خيارات إضافية ===== */
    .login-options {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 1.5rem 0 1rem;
        font-size: 0.9rem;
    }

    .form-check {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .form-check-input {
        width: 18px;
        height: 18px;
        border: 2px solid #e2e8f0;
        border-radius: 4px;
        cursor: pointer;
    }

    .form-check-label {
        color: #64748b;
        cursor: pointer;
        user-select: none;
    }

    .forgot-password {
        color: var(--primary-blue);
        text-decoration: none;
        font-size: 0.9rem;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .forgot-password:hover {
        color: var(--primary-blue-dark);
        text-decoration: underline;
    }

    /* ===== ذيل البطاقة ===== */
    .login-footer {
        background: var(--light-gray);
        padding: 1.5rem 2.5rem;
        text-align: center;
        border-top: 1px solid #e2e8f0;
    }

    .login-footer-text {
        color: #64748b;
        font-size: 0.85rem;
        margin: 0;
        line-height: 1.4;
    }

    /* ===== الرسوم المتحركة ===== */
    @keyframes gradientMove {
        0%, 100% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
    }

    @keyframes backgroundMove {
        0% { transform: translateX(0) translateY(0); }
        25% { transform: translateX(-25px) translateY(-25px); }
        50% { transform: translateX(-50px) translateY(0); }
        75% { transform: translateX(-25px) translateY(25px); }
        100% { transform: translateX(0) translateY(0); }
    }

    @keyframes fadeInUp {
        0% {
            opacity: 0;
            transform: translateY(20px);
        }
        100% {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* ===== تأثير الظهور ===== */
    .fade-in {
        animation: fadeInUp 0.6s ease-out;
    }

    /* ===== الأنماط المتجاوبة ===== */
    @media (max-width: 768px) {
        .login-container {
            padding: 1rem 0.5rem;
        }

        .login-card {
            max-width: 100%;
            margin: 0;
            border-radius: 15px;
        }

        .login-header {
            padding: 2rem 1.5rem;
        }

        .login-logo {
            width: 70px;
            height: 70px;
            margin-bottom: 1rem;
        }

        .login-logo svg {
            width: 40px;
            height: 40px;
        }

        .login-title {
            font-size: 1.5rem;
            margin-bottom: 0.3rem;
        }

        .login-subtitle {
            font-size: 0.85rem;
        }

        .login-body {
            padding: 2rem 1.5rem;
        }

        .form-control-login {
            height: 55px;
            padding: 0 2.5rem 0 3rem;
            font-size: 0.95rem;
        }

        .form-icon {
            left: 1rem;
            font-size: 1rem;
        }

        .form-label-login {
            left: 3rem;
            font-size: 0.9rem;
        }

        .form-control-login:focus + .form-label-login,
        .form-control-login:not(:placeholder-shown) + .form-label-login {
            left: 0.8rem;
            font-size: 0.75rem;
        }

        .login-btn {
            height: 45px;
            font-size: 0.95rem;
        }

        .login-options {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
        }

        .login-footer {
            padding: 1.2rem 1.5rem;
        }

        .login-footer-text {
            font-size: 0.8rem;
        }
    }

    @media (max-width: 480px) {
        .login-container {
            padding: 0.5rem 0.25rem;
        }

        .login-card {
            border-radius: 12px;
        }

        .login-header {
            padding: 1.5rem 1rem;
        }

        .login-body {
            padding: 1.5rem 1rem;
        }

        .login-footer {
            padding: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="login-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12">
                <div class="login-card fade-in">
                    <!-- رأس البطاقة -->
                    <div class="login-header">
                        <div class="login-logo">
                            <svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="50" cy="50" r="45" fill="rgba(255,255,255,0.9)" stroke="rgba(14, 165, 233, 0.5)" stroke-width="2"/>
                                <rect x="42" y="25" width="16" height="50" fill="#0ea5e9" rx="2"/>
                                <rect x="25" y="42" width="50" height="16" fill="#0ea5e9" rx="2"/>
                                <circle cx="50" cy="50" r="8" fill="white"/>
                            </svg>
                        </div>
                        <h1 class="login-title">مرحباً بك</h1>
                        <p class="login-subtitle">نظام إدارة الموظفين في المنشأة - نظام شامل ومتكامل</p>
                    </div>

                    <!-- جسم البطاقة -->
                    <div class="login-body">
                        <form method="POST" action="{{ url_for('login') }}">
                            <!-- حقل اسم المستخدم -->
                            <div class="form-group-login">
                                <i class="fas fa-user form-icon"></i>
                                <input type="text" class="form-control-login" id="inputUsername"
                                       name="username" placeholder=" " required autocomplete="username">
                                <label for="inputUsername" class="form-label-login">اسم المستخدم</label>
                            </div>

                            <!-- حقل كلمة المرور -->
                            <div class="form-group-login">
                                <i class="fas fa-lock form-icon"></i>
                                <input type="password" class="form-control-login" id="inputPassword"
                                       name="password" placeholder=" " required autocomplete="current-password">
                                <label for="inputPassword" class="form-label-login">كلمة المرور</label>
                                <button type="button" class="password-toggle" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>

                            <!-- خيارات إضافية -->
                            <div class="login-options">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="rememberMe">
                                    <label class="form-check-label" for="rememberMe">تذكرني</label>
                                </div>
                                <a href="#" class="forgot-password">
                                    <i class="fas fa-key"></i>
                                    نسيت كلمة المرور؟
                                </a>
                            </div>

                            <!-- زر تسجيل الدخول -->
                            <button type="submit" class="login-btn">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                تسجيل الدخول
                            </button>
                        </form>
                    </div>

                    <!-- ذيل البطاقة -->
                    <div class="login-footer">
                        <p class="login-footer-text">
                            <i class="fas fa-shield-alt me-1"></i>
                            نظام آمن ومحمي - متاح 24/7
                        </p>
                        <small class="login-footer-text">
                            جميع الحقوق محفوظة © 2024 - شركة العميس الطبية
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تبديل إظهار/إخفاء كلمة المرور
    const togglePassword = document.getElementById('togglePassword');
    const passwordInput = document.getElementById('inputPassword');

    togglePassword.addEventListener('click', function() {
        const icon = this.querySelector('i');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            passwordInput.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });

    // تحسين تجربة المستخدم - التركيز التلقائي
    document.getElementById('inputUsername').focus();

    // تأثير الضغط على زر تسجيل الدخول
    const loginBtn = document.querySelector('.login-btn');
    loginBtn.addEventListener('mousedown', function() {
        this.style.transform = 'translateY(-1px) scale(0.98)';
    });

    loginBtn.addEventListener('mouseup', function() {
        this.style.transform = 'translateY(-2px) scale(1)';
    });

    loginBtn.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
    });
});
</script>
{% endblock %}
