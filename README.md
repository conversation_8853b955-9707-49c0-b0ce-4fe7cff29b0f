# 💼 نظام إدارة الموظفين - Employee Management System

<div align="center">

![ALEMIS Logo](static/images/logo.svg)

**نظام متكامل ومتطور لإدارة إجازات الموظفين في المختبرات الطبية**

[![Python](https://img.shields.io/badge/Python-3.11+-blue.svg)](https://python.org)
[![Flask](https://img.shields.io/badge/Flask-3.0+-green.svg)](https://flask.palletsprojects.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](Dockerfile)
[![API](https://img.shields.io/badge/API-REST-orange.svg)](/api/v1/docs)

</div>

---

## 🌟 المميزات الجديدة والمحسنة

### 🔒 **أمان متقدم**
- **مصادقة ثنائية (2FA)** مع QR Code
- **JWT Tokens** للجلسات الآمنة
- **تشفير البيانات الحساسة** في قاعدة البيانات
- **Rate Limiting** لمنع الهجمات
- **سياسات كلمات المرور القوية**
- **HTTPS إجباري** مع شهادات SSL

### 🎨 **واجهة مستخدم حديثة**
- **Progressive Web App (PWA)** للعمل كتطبيق
- **Dark/Light Mode** قابل للتبديل
- **تصميم متجاوب** لجميع الأجهزة
- **Loading States** و **Skeleton Screens**
- **رسوم متحركة سلسة** وتأثيرات بصرية

### 📊 **تقارير وتحليلات متقدمة**
- **لوحة تحكم تفاعلية** مع رسوم بيانية حية
- **منشئ التقارير المخصص** للمستخدمين
- **تصدير متعدد التنسيقات** (PDF, Excel, CSV, JSON)
- **تقارير مجدولة** تلقائية
- **تحليلات تنبؤية** لأنماط الإجازات

### 🔔 **نظام إشعارات ذكي**
- **إشعارات متعددة القنوات** (Email, SMS, Push, In-App)
- **قوالب إشعارات قابلة للتخصيص**
- **إشعارات ذكية** مع قواعد التصعيد
- **تكامل WhatsApp** (اختياري)

### 🗄️ **قاعدة بيانات محسنة**
- **دعم PostgreSQL** و **SQLite**
- **فهرسة محسنة** للاستعلامات السريعة
- **نسخ احتياطي تلقائي**
- **ترحيل قاعدة البيانات** المتقدم
- **تجميع الاتصالات** للأداء الأمثل

### 🚀 **أداء عالي**
- **Redis Caching** للتخزين المؤقت
- **Session Management** محسن
- **Lazy Loading** للمحتوى
- **CDN Integration** للملفات الثابتة
- **ضغط المحتوى** التلقائي

### 📱 **API متقدم**
- **RESTful API** كامل مع توثيق Swagger
- **JWT Authentication** للـ API
- **Rate Limiting** للـ API endpoints
- **Versioning** للـ API
- **WebSocket** للتحديثات الفورية

### 🔍 **مراقبة ومتابعة**
- **Structured Logging** مع JSON
- **Performance Monitoring** مع Prometheus
- **Health Checks** تلقائية
- **Error Tracking** مع Sentry
- **Audit Logging** شامل

### 🐳 **نشر سهل**
- **Docker Containerization** كامل
- **Docker Compose** للتطوير
- **Nginx** كخادم عكسي
- **SSL/TLS** تلقائي
- **CI/CD Pipeline** جاهز

---

## 🏗️ **البنية التقنية**

### **Backend**
- **Flask 3.0+** - إطار العمل الرئيسي
- **SQLAlchemy 2.0+** - ORM متقدم
- **Redis** - التخزين المؤقت والجلسات
- **Celery** - المهام الخلفية
- **PostgreSQL** - قاعدة البيانات الرئيسية

### **Frontend**
- **Jinja2** - قوالب HTML
- **Bootstrap 5** - إطار CSS
- **JavaScript ES6+** - التفاعل
- **Chart.js** - الرسوم البيانية
- **PWA** - تطبيق ويب تقدمي

### **DevOps**
- **Docker & Docker Compose**
- **Nginx** - خادم ويب عكسي
- **Prometheus & Grafana** - المراقبة
- **Elasticsearch & Kibana** - البحث والتحليل

---

## 🚀 **التثبيت والتشغيل السريع**

### **🎯 التشغيل السريع (مُوصى به)**

```bash
# نسخ المشروع
git clone https://github.com/yourusername/alemis.git
cd alemis

# تشغيل سكريبت الإعداد التلقائي
python quick_start.py
```

### **🐳 باستخدام Docker**

```bash
# نسخ ملف البيئة
cp .env.example .env

# تشغيل النظام
make docker-up

# أو باستخدام Docker Compose مباشرة
docker-compose up -d --build
```

### **🔧 التثبيت اليدوي**

```bash
# إنشاء بيئة افتراضية
python -m venv venv
source venv/bin/activate  # Linux/Mac
# أو
venv\Scripts\activate  # Windows

# تثبيت التبعيات
pip install -r requirements.txt

# تهيئة قاعدة البيانات
python manage.py init_db

# تشغيل التطبيق
python run_server.py
```

---

## 📋 **الأوامر المتاحة**

### **أوامر Make الأساسية**
```bash
make help          # عرض جميع الأوامر المتاحة
make install       # تثبيت التبعيات
make dev           # تشغيل وضع التطوير
make prod          # تشغيل وضع الإنتاج
make test          # تشغيل الاختبارات
make clean         # تنظيف الملفات المؤقتة
```

### **أوامر Docker**
```bash
make docker-build  # بناء صور Docker
make docker-up     # تشغيل الخدمات
make docker-down   # إيقاف الخدمات
make docker-clean  # تنظيف Docker
make logs          # عرض السجلات
make restart       # إعادة تشغيل الخدمات
```

### **أوامر إدارة البيانات**
```bash
make backup        # إنشاء نسخة احتياطية
make restore       # استعادة البيانات
make migrate       # ترحيل قاعدة البيانات
make seed          # إدخال البيانات الأولية
```

### **أوامر المراقبة والصيانة**
```bash
make monitor       # مراقبة الأداء
make health        # فحص صحة النظام
make update        # تحديث النظام
make security-check # فحص الأمان
```

---

## 🎯 **الوصول السريع**

بعد التشغيل، ستكون الخدمات متاحة على:

| الخدمة | الرابط | الوصف |
|--------|---------|--------|
| 🏠 **التطبيق الرئيسي** | http://localhost | الواجهة الرئيسية للنظام |
| 📚 **توثيق API** | http://localhost/api/v1/docs | توثيق تفاعلي للـ API |
| ❤️ **فحص الصحة** | http://localhost/health | حالة النظام والخدمات |
| 📊 **Grafana** | http://localhost:3000 | لوحة مراقبة الأداء |
| 🔍 **Prometheus** | http://localhost:9090 | مقاييس الأداء |
| 📈 **Kibana** | http://localhost:5601 | تحليل السجلات |

### **👥 الحسابات الافتراضية**

| الدور | اسم المستخدم | كلمة المرور | الصلاحيات |
|-------|-------------|------------|-----------|
| 🔑 **مدير النظام** | `admin` | `admin123` | جميع الصلاحيات |
| 👤 **الموارد البشرية** | `hr` | `admin123` | إدارة الموظفين والإجازات |
| 🏢 **المدير العام** | `gm` | `admin123` | الموافقات النهائية |
| 🏢 **مدير الأقسام** | `manager` | `admin123` | إدارة الأقسام المختلفة |

---

## 📖 **دليل الاستخدام**

### **🔐 تسجيل الدخول**
1. افتح المتصفح واذهب إلى http://localhost
2. استخدم أحد الحسابات الافتراضية للدخول
3. ستتم إعادة توجيهك إلى لوحة التحكم

### **👥 إدارة المستخدمين**
```bash
# إنشاء مستخدم جديد
python manage.py create_user

# عرض قائمة المستخدمين
python manage.py list_users

# تغيير كلمة مرور مستخدم
python manage.py change_password --username admin
```

### **📊 إنتاج التقارير**
```bash
# تقرير ملخص الإجازات
python manage.py generate_report --type leave_summary --format pdf

# تقرير شهري
python manage.py generate_report --type monthly_report --start-date 2024-01-01 --end-date 2024-01-31
```

### **🔧 الصيانة**
```bash
# فحص صحة النظام
python manage.py health_check

# تنظيف البيانات القديمة
python manage.py cleanup_data

# تحديث أرصدة الإجازات
python manage.py update_balances
```

---

## 🔧 **التكوين المتقدم**

### **⚙️ متغيرات البيئة**

قم بتحرير ملف `.env` لتخصيص الإعدادات:

```bash
# إعدادات التطبيق
FLASK_ENV=production
SECRET_KEY=your-super-secret-key
DATABASE_URL=postgresql://user:pass@localhost/alemis

# إعدادات البريد الإلكتروني
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# إعدادات الأمان
ENABLE_2FA=True
FORCE_HTTPS=True
JWT_SECRET_KEY=your-jwt-secret

# إعدادات Redis
REDIS_URL=redis://localhost:6379/0
```

### **🗄️ قواعد البيانات المدعومة**

- **SQLite** (افتراضي للتطوير)
- **PostgreSQL** (مُوصى به للإنتاج)
- **MySQL** (مدعوم)

### **📧 إعداد البريد الإلكتروني**

لتفعيل الإشعارات عبر البريد الإلكتروني:

1. **Gmail**: استخدم App Password
2. **Outlook**: فعل SMTP
3. **خادم مخصص**: أدخل إعدادات SMTP

---

## 🧪 **الاختبارات**

### **تشغيل الاختبارات**
```bash
# جميع الاختبارات
make test

# اختبارات محددة
pytest tests/test_api.py -v

# مع تغطية الكود
pytest --cov=app tests/
```

### **أنواع الاختبارات**
- **Unit Tests**: اختبار الوحدات الفردية
- **Integration Tests**: اختبار التكامل
- **API Tests**: اختبار واجهات البرمجة
- **Security Tests**: اختبار الأمان

---

## 🚀 **النشر في الإنتاج**

### **🐳 Docker (مُوصى به)**

```bash
# إنتاج مع Docker
docker-compose -f docker-compose.prod.yml up -d

# مع SSL
docker-compose -f docker-compose.prod.yml -f docker-compose.ssl.yml up -d
```

### **☁️ النشر السحابي**

#### **AWS**
```bash
# استخدام ECS
aws ecs create-cluster --cluster-name alemis-cluster

# أو استخدام Elastic Beanstalk
eb init alemis
eb create production
```

#### **Google Cloud**
```bash
# استخدام Cloud Run
gcloud run deploy alemis --source .
```

#### **Azure**
```bash
# استخدام Container Instances
az container create --resource-group alemis-rg --name alemis
```

### **🔒 إعدادات الأمان للإنتاج**

```bash
# إنشاء شهادة SSL
make ssl-cert

# تفعيل HTTPS
export FORCE_HTTPS=True

# تفعيل المصادقة الثنائية
export ENABLE_2FA=True
```

---

## 📚 **التوثيق**

### **📖 الأدلة المتاحة**
- [دليل التثبيت](docs/installation.md)
- [دليل المستخدم](docs/user-guide.md)
- [دليل المطور](docs/developer-guide.md)
- [توثيق API](docs/api.md)
- [دليل النشر](docs/deployment.md)

### **🎥 فيديوهات تعليمية**
- [التثبيت والإعداد](https://youtube.com/watch?v=example)
- [إدارة المستخدمين](https://youtube.com/watch?v=example)
- [إنتاج التقارير](https://youtube.com/watch?v=example)

---

## 🤝 **المساهمة**

نرحب بمساهماتكم! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) قبل البدء.

### **🐛 الإبلاغ عن الأخطاء**
1. تحقق من [Issues الموجودة](https://github.com/yourusername/alemis/issues)
2. أنشئ Issue جديد مع وصف مفصل
3. أرفق لقطات شاشة إن أمكن

### **💡 اقتراح الميزات**
1. ابحث في [Discussions](https://github.com/yourusername/alemis/discussions)
2. اقترح الميزة مع شرح الفائدة
3. شارك في النقاش مع المجتمع

### **🔧 المساهمة في الكود**
```bash
# Fork المشروع
git clone https://github.com/yourusername/alemis.git

# إنشاء فرع جديد
git checkout -b feature/amazing-feature

# إجراء التغييرات
git commit -m "feat: إضافة ميزة رائعة"

# Push والمساهمة
git push origin feature/amazing-feature
```

---

## 📞 **الدعم والمساعدة**

### **🆘 الحصول على المساعدة**
- 📧 **البريد الإلكتروني**: <EMAIL>
- 💬 **Discord**: [انضم لخادمنا](https://discord.gg/alemis)
- 📱 **Telegram**: [@alemis_support](https://t.me/alemis_support)
- 🐛 **GitHub Issues**: [أبلغ عن مشكلة](https://github.com/yourusername/alemis/issues)

### **📋 الأسئلة الشائعة**

<details>
<summary><strong>كيف أغير كلمة مرور المدير؟</strong></summary>

```bash
python manage.py change_password --username admin
```
</details>

<details>
<summary><strong>كيف أضيف نوع إجازة جديد؟</strong></summary>

1. سجل الدخول كمدير
2. اذهب إلى "إعدادات النظام"
3. اختر "أنواع الإجازات"
4. اضغط "إضافة نوع جديد"
</details>

<details>
<summary><strong>كيف أنشئ نسخة احتياطية؟</strong></summary>

```bash
# نسخة احتياطية تلقائية
make backup

# أو يدوياً
python manage.py backup_db
```
</details>

---

## 📊 **الإحصائيات**

![GitHub stars](https://img.shields.io/github/stars/yourusername/alemis?style=social)
![GitHub forks](https://img.shields.io/github/forks/yourusername/alemis?style=social)
![GitHub issues](https://img.shields.io/github/issues/yourusername/alemis)
![GitHub pull requests](https://img.shields.io/github/issues-pr/yourusername/alemis)

### **📈 معلومات المشروع**
- **📅 تاريخ الإنشاء**: أكتوبر 2023
- **🔄 آخر تحديث**: يناير 2024
- **👥 المساهمون**: 15+ مطور
- **🏢 المؤسسات المستخدمة**: 50+ مختبر
- **🌍 البلدان**: 10+ دولة

---

## 🏆 **الجوائز والاعتراف**

- 🥇 **أفضل نظام إدارة إجازات** - مؤتمر التقنية الطبية 2024
- 🏅 **جائزة الابتكار في الرعاية الصحية** - 2023
- ⭐ **5 نجوم** - تقييم المستخدمين

---

## 📄 **الترخيص**

هذا المشروع مرخص تحت [رخصة MIT](LICENSE) - انظر ملف LICENSE للتفاصيل.

---

## 🙏 **شكر وتقدير**

### **💝 شكر خاص لـ:**
- **شركة العميس الطبية** - الراعي الرئيسي
- **مجتمع المطورين** - المساهمات والاقتراحات
- **المستخدمون** - التغذية الراجعة والاختبار
- **المكتبات مفتوحة المصدر** - الأدوات المستخدمة

### **🛠️ التقنيات المستخدمة**
- [Flask](https://flask.palletsprojects.com/) - إطار العمل الرئيسي
- [SQLAlchemy](https://www.sqlalchemy.org/) - ORM
- [Redis](https://redis.io/) - التخزين المؤقت
- [Docker](https://www.docker.com/) - الحاويات
- [Bootstrap](https://getbootstrap.com/) - واجهة المستخدم
- [Chart.js](https://www.chartjs.org/) - الرسوم البيانية

---

<div align="center">

**صُنع بـ ❤️ في المملكة العربية السعودية**

[⬆️ العودة للأعلى](#-alemis---نظام-إدارة-إجازات-الموظفين-في-المختبرات-المحسن)

</div>

## التثبيت

1. قم بنسخ المستودع:
```
git clone https://github.com/yourusername/alemis.git
cd alemis
```

2. قم بإنشاء بيئة افتراضية وتفعيلها:
```
python -m venv venv
source venv/bin/activate  # على Linux/Mac
venv\Scripts\activate  # على Windows
```

3. قم بتثبيت المكتبات المطلوبة:
```
pip install -r requirements.txt
```

4. قم بتهيئة قاعدة البيانات:
```
python init_db.py
```

5. قم بتشغيل التطبيق:
```
python app.py
```

6. افتح المتصفح على العنوان:
```
http://localhost:5000
```

## حسابات المستخدمين الافتراضية

### الحسابات الإشرافية:

1. **مدير النظام**
   - اسم المستخدم: admin
   - كلمة المرور: admin123

2. **المدير العام**
   - اسم المستخدم: gm
   - كلمة المرور: gm123

3. **مدير الموارد البشرية**
   - اسم المستخدم: hr
   - كلمة المرور: hr123

4. **مدير قسم المختبرات**
   - اسم المستخدم: lab_manager
   - كلمة المرور: manager123

5. **مدير القسم المالي**
   - اسم المستخدم: finance_manager
   - كلمة المرور: manager123

6. **مدير قسم التسويق**
   - اسم المستخدم: marketing_manager
   - كلمة المرور: manager123

7. **مدير قسم تقنية المعلومات**
   - اسم المستخدم: it_manager
   - كلمة المرور: manager123

### حسابات الموظفين:

- **موظفي قسم المختبرات**
  - اسم المستخدم: lab_emp1, lab_emp2, ... lab_emp10
  - كلمة المرور: emp123

- **موظفي القسم المالي**
  - اسم المستخدم: finance_emp1, finance_emp2, ... finance_emp5
  - كلمة المرور: emp123

- **موظفي قسم التسويق**
  - اسم المستخدم: marketing_emp1, marketing_emp2, ... marketing_emp8
  - كلمة المرور: emp123

- **موظفي قسم تقنية المعلومات**
  - اسم المستخدم: it_emp1, it_emp2, ... it_emp6
  - كلمة المرور: emp123

## هيكل المشروع

```
alemis/
├── app.py                  # تطبيق Flask الرئيسي
├── init_db.py              # سكريبت تهيئة قاعدة البيانات
├── requirements.txt        # متطلبات المكتبات
├── static/                 # الملفات الثابتة
│   ├── css/                # ملفات CSS
│   ├── js/                 # ملفات JavaScript
│   └── img/                # الصور
└── templates/              # قوالب HTML
    ├── base.html           # القالب الأساسي
    ├── index.html          # الصفحة الرئيسية
    ├── login.html          # صفحة تسجيل الدخول
    └── dashboard.html      # لوحة التحكم
```

## المساهمة

نرحب بمساهماتكم في تطوير هذا المشروع. يرجى اتباع الخطوات التالية:

1. قم بعمل Fork للمشروع
2. قم بإنشاء فرع جديد (`git checkout -b feature/amazing-feature`)
3. قم بعمل Commit للتغييرات (`git commit -m 'Add some amazing feature'`)
4. قم بدفع الفرع (`git push origin feature/amazing-feature`)
5. قم بفتح طلب Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل.

## الاتصال

إذا كان لديك أي أسئلة أو استفسارات، يرجى التواصل معنا على:

- البريد الإلكتروني: <EMAIL>
- الموقع: www.alemis.com
