/* ===== مكونات واجهة المستخدم المتقدمة ===== */
/* Advanced UI Components */

/* ===== البطاقات التفاعلية المتقدمة ===== */
.interactive-card {
    background: var(--white);
    border: var(--border-light);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    overflow: hidden;
    position: relative;
    cursor: pointer;
}

.interactive-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 102, 204, 0.1), transparent);
    transition: var(--transition-slow);
}

.interactive-card:hover::before {
    left: 100%;
}

.interactive-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-blue);
}

.card-header-professional {
    padding: var(--space-xl);
    border-bottom: var(--border-light);
    background: linear-gradient(135deg, var(--off-white), var(--light-gray));
    position: relative;
}

.card-body-professional {
    padding: var(--space-xl);
}

.card-footer-professional {
    padding: var(--space-lg) var(--space-xl);
    border-top: var(--border-light);
    background: var(--off-white);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* ===== الأزرار المتقدمة ===== */
.btn-advanced {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
    padding: var(--space-md) var(--space-xl);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    line-height: 1.5;
    border: 2px solid transparent;
    border-radius: var(--radius-lg);
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-fast);
    min-height: 48px;
    position: relative;
    overflow: hidden;
}

.btn-advanced::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: var(--transition-normal);
}

.btn-advanced:hover::before {
    width: 300px;
    height: 300px;
}

.btn-advanced:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.25);
}

.btn-gradient-primary {
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-teal));
    color: var(--white);
    border-color: var(--primary-blue);
}

.btn-gradient-primary:hover {
    background: linear-gradient(135deg, var(--primary-blue-dark), var(--secondary-teal));
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-gradient-success {
    background: linear-gradient(135deg, var(--success), var(--accent-green));
    color: var(--white);
    border-color: var(--success);
}

.btn-gradient-warning {
    background: linear-gradient(135deg, var(--warning), var(--accent-orange));
    color: var(--white);
    border-color: var(--warning);
}

.btn-gradient-danger {
    background: linear-gradient(135deg, var(--danger), var(--accent-red));
    color: var(--white);
    border-color: var(--danger);
}

/* ===== النماذج المتقدمة ===== */
.form-group-advanced {
    margin-bottom: var(--space-xl);
    position: relative;
}

.form-label-advanced {
    display: block;
    margin-bottom: var(--space-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--near-black);
    font-size: var(--font-size-base);
}

.form-control-advanced {
    display: block;
    width: 100%;
    padding: var(--space-md) var(--space-lg);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: 1.5;
    color: var(--near-black);
    background-color: var(--white);
    border: 2px solid var(--medium-gray);
    border-radius: var(--radius-lg);
    transition: var(--transition-fast);
    position: relative;
}

.form-control-advanced:focus {
    border-color: var(--primary-blue);
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
    background-color: var(--off-white);
}

.form-control-advanced::placeholder {
    color: var(--dark-gray);
    opacity: 0.7;
}

/* حقول الإدخال العائمة */
.floating-label {
    position: relative;
}

.floating-label .form-control-advanced {
    padding-top: var(--space-xl);
}

.floating-label .form-label-advanced {
    position: absolute;
    top: var(--space-md);
    right: var(--space-lg);
    font-size: var(--font-size-base);
    color: var(--dark-gray);
    transition: var(--transition-fast);
    pointer-events: none;
    z-index: 2;
}

.floating-label .form-control-advanced:focus + .form-label-advanced,
.floating-label .form-control-advanced:not(:placeholder-shown) + .form-label-advanced {
    top: var(--space-xs);
    font-size: var(--font-size-sm);
    color: var(--primary-blue);
    font-weight: var(--font-weight-semibold);
}

/* ===== القوائم المنسدلة المتقدمة ===== */
.select-advanced {
    position: relative;
    display: block;
}

.select-advanced select {
    appearance: none;
    background: var(--white) url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%236b7280'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E") no-repeat left var(--space-lg) center;
    background-size: 20px;
    padding-left: 3rem;
}

.select-advanced select:focus {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%230066cc'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
}

/* ===== مربعات الاختيار المتقدمة ===== */
.checkbox-advanced {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    cursor: pointer;
    user-select: none;
}

.checkbox-advanced input[type="checkbox"] {
    appearance: none;
    width: 20px;
    height: 20px;
    border: 2px solid var(--medium-gray);
    border-radius: var(--radius-sm);
    background: var(--white);
    cursor: pointer;
    position: relative;
    transition: var(--transition-fast);
}

.checkbox-advanced input[type="checkbox"]:checked {
    background: var(--primary-blue);
    border-color: var(--primary-blue);
}

.checkbox-advanced input[type="checkbox"]:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--white);
    font-size: 12px;
    font-weight: bold;
}

.checkbox-advanced input[type="checkbox"]:focus {
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.25);
}

/* ===== أزرار الراديو المتقدمة ===== */
.radio-advanced {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    cursor: pointer;
    user-select: none;
}

.radio-advanced input[type="radio"] {
    appearance: none;
    width: 20px;
    height: 20px;
    border: 2px solid var(--medium-gray);
    border-radius: 50%;
    background: var(--white);
    cursor: pointer;
    position: relative;
    transition: var(--transition-fast);
}

.radio-advanced input[type="radio"]:checked {
    border-color: var(--primary-blue);
}

.radio-advanced input[type="radio"]:checked::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 10px;
    height: 10px;
    background: var(--primary-blue);
    border-radius: 50%;
}

.radio-advanced input[type="radio"]:focus {
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.25);
}

/* ===== مفاتيح التبديل ===== */
.switch-advanced {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch-advanced input {
    opacity: 0;
    width: 0;
    height: 0;
}

.switch-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--medium-gray);
    transition: var(--transition-fast);
    border-radius: 34px;
}

.switch-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: var(--white);
    transition: var(--transition-fast);
    border-radius: 50%;
    box-shadow: var(--shadow-sm);
}

.switch-advanced input:checked + .switch-slider {
    background-color: var(--primary-blue);
}

.switch-advanced input:checked + .switch-slider:before {
    transform: translateX(26px);
}

.switch-advanced input:focus + .switch-slider {
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.25);
}

/* ===== التبويبات المتقدمة ===== */
.tabs-advanced {
    border-bottom: 2px solid var(--light-gray);
    margin-bottom: var(--space-xl);
}

.tabs-nav {
    display: flex;
    gap: var(--space-lg);
    list-style: none;
    margin: 0;
    padding: 0;
}

.tab-item {
    position: relative;
}

.tab-link {
    display: block;
    padding: var(--space-lg) var(--space-xl);
    color: var(--dark-gray);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    transition: var(--transition-fast);
    position: relative;
}

.tab-link:hover {
    color: var(--primary-blue);
    background: var(--off-white);
}

.tab-link.active {
    color: var(--primary-blue);
    background: var(--white);
    border-bottom: 3px solid var(--primary-blue);
}

.tab-content {
    padding: var(--space-xl) 0;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
    animation: fadeInUp 0.5s ease-out;
}
