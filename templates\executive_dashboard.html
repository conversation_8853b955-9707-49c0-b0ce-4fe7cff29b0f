{% extends "base.html" %}

{% block title %}نظام إدارة الموظفين - لوحة التحكم التنفيذية{% endblock %}

{% block styles %}
<style>
    .executive-header {
        background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-teal) 50%, var(--accent-green) 100%);
        color: var(--white);
        padding: var(--space-2xl) 0;
        margin-bottom: var(--space-2xl);
        border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);
        position: relative;
        overflow: hidden;
    }

    .executive-header::before {
        content: '';
        position: absolute;
        top: -30%;
        right: -10%;
        width: 300px;
        height: 300px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='rgba(255,255,255,0.1)'%3E%3Cpath d='M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z'/%3E%3C/svg%3E") no-repeat center;
        background-size: contain;
        animation: dashboardFloat 12s ease-in-out infinite;
    }

    .kpi-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: var(--space-lg);
        margin-bottom: var(--space-2xl);
    }

    .kpi-card {
        background: var(--white);
        border: var(--border-light);
        border-radius: var(--radius-2xl);
        box-shadow: var(--shadow-sm);
        transition: var(--transition-normal);
        overflow: hidden;
        position: relative;
        padding: var(--space-xl);
    }

    .kpi-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 6px;
        background: linear-gradient(90deg, var(--kpi-color, var(--primary-blue)), var(--kpi-color-light, var(--primary-blue-light)));
    }

    .kpi-card:hover {
        transform: translateY(-8px);
        box-shadow: var(--shadow-xl);
    }

    .kpi-card.employees { --kpi-color: var(--primary-blue); --kpi-color-light: var(--primary-blue-light); }
    .kpi-card.attendance { --kpi-color: var(--accent-green); --kpi-color-light: var(--success); }
    .kpi-card.leaves { --kpi-color: var(--warning); --kpi-color-light: var(--accent-orange); }
    .kpi-card.performance { --kpi-color: var(--accent-purple); --kpi-color-light: #8b5cf6; }
    .kpi-card.projects { --kpi-color: var(--secondary-teal); --kpi-color-light: var(--info); }
    .kpi-card.revenue { --kpi-color: var(--accent-green); --kpi-color-light: #10b981; }

    .kpi-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--space-lg);
    }

    .kpi-icon {
        width: 60px;
        height: 60px;
        border-radius: var(--radius-xl);
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, var(--kpi-color, var(--primary-blue)), var(--kpi-color-light, var(--primary-blue-light)));
        color: var(--white);
        font-size: var(--font-size-2xl);
    }

    .kpi-trend {
        display: flex;
        align-items: center;
        gap: var(--space-xs);
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-semibold);
    }

    .trend-up {
        color: var(--success);
    }

    .trend-down {
        color: var(--danger);
    }

    .kpi-value {
        font-size: var(--font-size-4xl);
        font-weight: var(--font-weight-bold);
        color: var(--kpi-color, var(--primary-blue));
        margin-bottom: var(--space-sm);
    }

    .kpi-label {
        font-size: var(--font-size-base);
        font-weight: var(--font-weight-semibold);
        color: var(--near-black);
        margin-bottom: var(--space-xs);
    }

    .kpi-description {
        font-size: var(--font-size-sm);
        color: var(--dark-gray);
    }

    .charts-section {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: var(--space-xl);
        margin-bottom: var(--space-2xl);
    }

    .chart-card {
        background: var(--white);
        border: var(--border-light);
        border-radius: var(--radius-2xl);
        box-shadow: var(--shadow-sm);
        padding: var(--space-xl);
    }

    .chart-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--space-lg);
        padding-bottom: var(--space-md);
        border-bottom: var(--border-light);
    }

    .chart-title {
        font-size: var(--font-size-xl);
        font-weight: var(--font-weight-bold);
        color: var(--near-black);
    }

    .chart-filter {
        display: flex;
        gap: var(--space-sm);
    }

    .filter-btn {
        padding: var(--space-xs) var(--space-md);
        border: 2px solid var(--medium-gray);
        border-radius: var(--radius-lg);
        background: var(--white);
        color: var(--dark-gray);
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
        cursor: pointer;
        transition: var(--transition-fast);
    }

    .filter-btn.active {
        background: var(--primary-blue);
        border-color: var(--primary-blue);
        color: var(--white);
    }

    .departments-overview {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--space-lg);
        margin-bottom: var(--space-2xl);
    }

    .dept-card {
        background: var(--white);
        border: var(--border-light);
        border-radius: var(--radius-xl);
        box-shadow: var(--shadow-sm);
        padding: var(--space-lg);
        transition: var(--transition-normal);
        position: relative;
        overflow: hidden;
    }

    .dept-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--dept-color, var(--primary-blue));
    }

    .dept-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-md);
    }

    .dept-card.general { --dept-color: var(--primary-blue); }
    .dept-card.hr { --dept-color: var(--accent-green); }
    .dept-card.finance { --dept-color: var(--warning); }
    .dept-card.it { --dept-color: var(--accent-purple); }
    .dept-card.marketing { --dept-color: var(--accent-red); }
    .dept-card.lab { --dept-color: var(--secondary-teal); }

    .dept-header {
        display: flex;
        align-items: center;
        gap: var(--space-md);
        margin-bottom: var(--space-md);
    }

    .dept-icon {
        width: 40px;
        height: 40px;
        border-radius: var(--radius-lg);
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--dept-color, var(--primary-blue));
        color: var(--white);
        font-size: var(--font-size-lg);
    }

    .dept-name {
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-semibold);
        color: var(--near-black);
    }

    .dept-stats {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .dept-stat {
        text-align: center;
    }

    .dept-stat-value {
        font-size: var(--font-size-xl);
        font-weight: var(--font-weight-bold);
        color: var(--dept-color, var(--primary-blue));
        display: block;
    }

    .dept-stat-label {
        font-size: var(--font-size-xs);
        color: var(--dark-gray);
        margin-top: var(--space-xs);
    }

    .recent-activities {
        background: var(--white);
        border: var(--border-light);
        border-radius: var(--radius-2xl);
        box-shadow: var(--shadow-sm);
        padding: var(--space-xl);
    }

    .activity-item {
        display: flex;
        align-items: center;
        gap: var(--space-md);
        padding: var(--space-md) 0;
        border-bottom: 1px solid var(--light-gray);
    }

    .activity-item:last-child {
        border-bottom: none;
    }

    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--activity-color, var(--primary-blue));
        color: var(--white);
        font-size: var(--font-size-sm);
    }

    .activity-content {
        flex: 1;
    }

    .activity-title {
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-semibold);
        color: var(--near-black);
        margin-bottom: var(--space-xs);
    }

    .activity-time {
        font-size: var(--font-size-xs);
        color: var(--dark-gray);
    }

    @keyframes dashboardFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        25% { transform: translateY(-15px) rotate(2deg); }
        50% { transform: translateY(-8px) rotate(-2deg); }
        75% { transform: translateY(-20px) rotate(1deg); }
    }

    /* التصميم المتجاوب */
    @media (max-width: 1200px) {
        .charts-section {
            grid-template-columns: 1fr;
        }
    }

    @media (max-width: 768px) {
        .kpi-grid {
            grid-template-columns: 1fr;
        }

        .departments-overview {
            grid-template-columns: 1fr;
        }

        .chart-header {
            flex-direction: column;
            gap: var(--space-md);
            align-items: flex-start;
        }

        .chart-filter {
            width: 100%;
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-professional">
    <!-- رأس لوحة التحكم التنفيذية -->
    <div class="executive-header">
        <div class="container-professional">
            <div class="text-center">
                <div class="dashboard-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h1 class="dashboard-title">لوحة التحكم التنفيذية</h1>
                <p class="dashboard-subtitle">مؤشرات الأداء الرئيسية وتحليلات شاملة لجميع أقسام المنشأة</p>
            </div>
        </div>
    </div>

    <!-- مؤشرات الأداء الرئيسية -->
    <div class="kpi-grid">
        <div class="kpi-card employees">
            <div class="kpi-header">
                <div class="kpi-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="kpi-trend trend-up">
                    <i class="fas fa-arrow-up"></i>
                    +5.2%
                </div>
            </div>
            <div class="kpi-value">247</div>
            <div class="kpi-label">إجمالي الموظفين</div>
            <div class="kpi-description">موزعين على 8 أقسام رئيسية</div>
        </div>

        <div class="kpi-card attendance">
            <div class="kpi-header">
                <div class="kpi-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="kpi-trend trend-up">
                    <i class="fas fa-arrow-up"></i>
                    +2.1%
                </div>
            </div>
            <div class="kpi-value">94.8%</div>
            <div class="kpi-label">معدل الحضور</div>
            <div class="kpi-description">متوسط الحضور الشهري</div>
        </div>

        <div class="kpi-card leaves">
            <div class="kpi-header">
                <div class="kpi-icon">
                    <i class="fas fa-calendar-times"></i>
                </div>
                <div class="kpi-trend trend-down">
                    <i class="fas fa-arrow-down"></i>
                    -1.3%
                </div>
            </div>
            <div class="kpi-value">23</div>
            <div class="kpi-label">طلبات الإجازة المعلقة</div>
            <div class="kpi-description">تحتاج إلى مراجعة</div>
        </div>

        <div class="kpi-card performance">
            <div class="kpi-header">
                <div class="kpi-icon">
                    <i class="fas fa-star"></i>
                </div>
                <div class="kpi-trend trend-up">
                    <i class="fas fa-arrow-up"></i>
                    +3.7%
                </div>
            </div>
            <div class="kpi-value">4.6</div>
            <div class="kpi-label">متوسط تقييم الأداء</div>
            <div class="kpi-description">من أصل 5 نقاط</div>
        </div>

        <div class="kpi-card projects">
            <div class="kpi-header">
                <div class="kpi-icon">
                    <i class="fas fa-project-diagram"></i>
                </div>
                <div class="kpi-trend trend-up">
                    <i class="fas fa-arrow-up"></i>
                    +8.4%
                </div>
            </div>
            <div class="kpi-value">18</div>
            <div class="kpi-label">المشاريع النشطة</div>
            <div class="kpi-description">قيد التنفيذ حالياً</div>
        </div>

        <div class="kpi-card revenue">
            <div class="kpi-header">
                <div class="kpi-icon">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <div class="kpi-trend trend-up">
                    <i class="fas fa-arrow-up"></i>
                    +12.3%
                </div>
            </div>
            <div class="kpi-value">89%</div>
            <div class="kpi-label">كفاءة الإنتاجية</div>
            <div class="kpi-description">مقارنة بالشهر الماضي</div>
        </div>
    </div>

    <!-- الرسوم البيانية -->
    <div class="charts-section">
        <div class="chart-card">
            <div class="chart-header">
                <h3 class="chart-title">تحليل الحضور والأداء</h3>
                <div class="chart-filter">
                    <button class="filter-btn active">الشهر</button>
                    <button class="filter-btn">الربع</button>
                    <button class="filter-btn">السنة</button>
                </div>
            </div>
            <div style="height: 300px; display: flex; align-items: center; justify-content: center; color: var(--dark-gray);">
                <i class="fas fa-chart-area" style="font-size: 3rem; margin-right: 1rem;"></i>
                سيتم إضافة الرسم البياني هنا
            </div>
        </div>

        <div class="chart-card">
            <div class="chart-header">
                <h3 class="chart-title">الأنشطة الأخيرة</h3>
            </div>
            <div class="recent-activities">
                <div class="activity-item">
                    <div class="activity-icon" style="--activity-color: var(--accent-green);">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">انضمام موظف جديد</div>
                        <div class="activity-time">منذ ساعتين</div>
                    </div>
                </div>
                <div class="activity-item">
                    <div class="activity-icon" style="--activity-color: var(--warning);">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">موافقة على طلب إجازة</div>
                        <div class="activity-time">منذ 4 ساعات</div>
                    </div>
                </div>
                <div class="activity-item">
                    <div class="activity-icon" style="--activity-color: var(--secondary-teal);">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">بدء مشروع جديد</div>
                        <div class="activity-time">أمس</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نظرة عامة على الأقسام -->
    <div class="departments-overview">
        <div class="dept-card general">
            <div class="dept-header">
                <div class="dept-icon">
                    <i class="fas fa-users-cog"></i>
                </div>
                <div class="dept-name">الإدارة العامة</div>
            </div>
            <div class="dept-stats">
                <div class="dept-stat">
                    <span class="dept-stat-value">12</span>
                    <div class="dept-stat-label">موظف</div>
                </div>
                <div class="dept-stat">
                    <span class="dept-stat-value">96%</span>
                    <div class="dept-stat-label">حضور</div>
                </div>
                <div class="dept-stat">
                    <span class="dept-stat-value">4.8</span>
                    <div class="dept-stat-label">أداء</div>
                </div>
            </div>
        </div>

        <div class="dept-card hr">
            <div class="dept-header">
                <div class="dept-icon">
                    <i class="fas fa-user-tie"></i>
                </div>
                <div class="dept-name">الموارد البشرية</div>
            </div>
            <div class="dept-stats">
                <div class="dept-stat">
                    <span class="dept-stat-value">8</span>
                    <div class="dept-stat-label">موظف</div>
                </div>
                <div class="dept-stat">
                    <span class="dept-stat-value">98%</span>
                    <div class="dept-stat-label">حضور</div>
                </div>
                <div class="dept-stat">
                    <span class="dept-stat-value">4.9</span>
                    <div class="dept-stat-label">أداء</div>
                </div>
            </div>
        </div>

        <div class="dept-card finance">
            <div class="dept-header">
                <div class="dept-icon">
                    <i class="fas fa-calculator"></i>
                </div>
                <div class="dept-name">المالية والمحاسبة</div>
            </div>
            <div class="dept-stats">
                <div class="dept-stat">
                    <span class="dept-stat-value">15</span>
                    <div class="dept-stat-label">موظف</div>
                </div>
                <div class="dept-stat">
                    <span class="dept-stat-value">94%</span>
                    <div class="dept-stat-label">حضور</div>
                </div>
                <div class="dept-stat">
                    <span class="dept-stat-value">4.7</span>
                    <div class="dept-stat-label">أداء</div>
                </div>
            </div>
        </div>

        <div class="dept-card it">
            <div class="dept-header">
                <div class="dept-icon">
                    <i class="fas fa-laptop-code"></i>
                </div>
                <div class="dept-name">تقنية المعلومات</div>
            </div>
            <div class="dept-stats">
                <div class="dept-stat">
                    <span class="dept-stat-value">22</span>
                    <div class="dept-stat-label">موظف</div>
                </div>
                <div class="dept-stat">
                    <span class="dept-stat-value">92%</span>
                    <div class="dept-stat-label">حضور</div>
                </div>
                <div class="dept-stat">
                    <span class="dept-stat-value">4.6</span>
                    <div class="dept-stat-label">أداء</div>
                </div>
            </div>
        </div>

        <div class="dept-card marketing">
            <div class="dept-header">
                <div class="dept-icon">
                    <i class="fas fa-bullhorn"></i>
                </div>
                <div class="dept-name">التسويق والمبيعات</div>
            </div>
            <div class="dept-stats">
                <div class="dept-stat">
                    <span class="dept-stat-value">18</span>
                    <div class="dept-stat-label">موظف</div>
                </div>
                <div class="dept-stat">
                    <span class="dept-stat-value">95%</span>
                    <div class="dept-stat-label">حضور</div>
                </div>
                <div class="dept-stat">
                    <span class="dept-stat-value">4.5</span>
                    <div class="dept-stat-label">أداء</div>
                </div>
            </div>
        </div>

        <div class="dept-card lab">
            <div class="dept-header">
                <div class="dept-icon">
                    <i class="fas fa-flask"></i>
                </div>
                <div class="dept-name">المختبرات</div>
            </div>
            <div class="dept-stats">
                <div class="dept-stat">
                    <span class="dept-stat-value">35</span>
                    <div class="dept-stat-label">موظف</div>
                </div>
                <div class="dept-stat">
                    <span class="dept-stat-value">97%</span>
                    <div class="dept-stat-label">حضور</div>
                </div>
                <div class="dept-stat">
                    <span class="dept-stat-value">4.8</span>
                    <div class="dept-stat-label">أداء</div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تفعيل أزرار التصفية
document.querySelectorAll('.filter-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        // إزالة الفئة النشطة من جميع الأزرار
        document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
        // إضافة الفئة النشطة للزر المضغوط
        this.classList.add('active');
        
        // هنا يمكن إضافة منطق تحديث البيانات
        console.log('تم تغيير التصفية إلى:', this.textContent);
    });
});

// تحديث البيانات كل 30 ثانية
setInterval(() => {
    console.log('تحديث البيانات...');
    // هنا يمكن إضافة منطق تحديث البيانات من الخادم
}, 30000);
</script>
{% endblock %}
