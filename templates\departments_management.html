{% extends "base.html" %}

{% block title %}نظام إدارة الموظفين - إدارة الأقسام{% endblock %}

{% block styles %}
<style>
    .departments-header {
        background: linear-gradient(135deg, var(--primary-blue), var(--secondary-teal));
        color: var(--white);
        padding: var(--space-2xl) 0;
        margin-bottom: var(--space-2xl);
        border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);
        position: relative;
        overflow: hidden;
    }

    .departments-header::before {
        content: '';
        position: absolute;
        top: -20%;
        right: -10%;
        width: 200px;
        height: 200px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='rgba(255,255,255,0.1)'%3E%3Cpath d='M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'/%3E%3C/svg%3E") no-repeat center;
        background-size: contain;
        animation: float 8s ease-in-out infinite;
    }

    .department-card {
        background: var(--white);
        border: var(--border-light);
        border-radius: var(--radius-2xl);
        box-shadow: var(--shadow-sm);
        transition: var(--transition-normal);
        overflow: hidden;
        position: relative;
        margin-bottom: var(--space-lg);
    }

    .department-card:hover {
        transform: translateY(-8px);
        box-shadow: var(--shadow-lg);
    }

    .department-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(90deg, var(--dept-color, var(--primary-blue)), var(--dept-color-light, var(--primary-blue-light)));
    }

    .department-card.lab { --dept-color: var(--secondary-teal); --dept-color-light: var(--info); }
    .department-card.radiology { --dept-color: var(--accent-purple); --dept-color-light: #8b5cf6; }

    .department-header {
        padding: var(--space-xl);
        display: flex;
        align-items: center;
        gap: var(--space-lg);
    }

    .department-icon {
        width: 80px;
        height: 80px;
        border-radius: var(--radius-2xl);
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, var(--dept-color, var(--primary-blue)), var(--dept-color-light, var(--primary-blue-light)));
        color: var(--white);
        font-size: var(--font-size-3xl);
        box-shadow: var(--shadow-md);
    }

    .department-info h3 {
        font-size: var(--font-size-2xl);
        font-weight: var(--font-weight-bold);
        color: var(--near-black);
        margin-bottom: var(--space-sm);
    }

    .department-info p {
        color: var(--dark-gray);
        font-size: var(--font-size-base);
        margin-bottom: var(--space-sm);
    }

    .department-stats {
        display: flex;
        gap: var(--space-lg);
        margin-top: var(--space-md);
    }

    .stat-item {
        text-align: center;
    }

    .stat-number {
        font-size: var(--font-size-xl);
        font-weight: var(--font-weight-bold);
        color: var(--dept-color, var(--primary-blue));
        display: block;
    }

    .stat-label {
        font-size: var(--font-size-sm);
        color: var(--dark-gray);
        margin-top: var(--space-xs);
    }

    .department-actions {
        padding: var(--space-lg) var(--space-xl);
        border-top: var(--border-light);
        background: var(--off-white);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .action-buttons {
        display: flex;
        gap: var(--space-sm);
    }

    .btn-department {
        padding: var(--space-sm) var(--space-lg);
        border-radius: var(--radius-lg);
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
        text-decoration: none;
        transition: var(--transition-fast);
        display: inline-flex;
        align-items: center;
        gap: var(--space-xs);
    }

    .btn-view {
        background: var(--primary-blue);
        color: var(--white);
        border: 2px solid var(--primary-blue);
    }

    .btn-view:hover {
        background: var(--primary-blue-dark);
        border-color: var(--primary-blue-dark);
        color: var(--white);
        text-decoration: none;
    }

    .btn-edit {
        background: var(--white);
        color: var(--warning);
        border: 2px solid var(--warning);
    }

    .btn-edit:hover {
        background: var(--warning);
        color: var(--white);
        text-decoration: none;
    }

    .btn-delete {
        background: var(--white);
        color: var(--accent-red);
        border: 2px solid var(--accent-red);
    }

    .btn-delete:hover {
        background: var(--accent-red);
        color: var(--white);
        text-decoration: none;
    }

    .department-status {
        display: inline-flex;
        align-items: center;
        gap: var(--space-xs);
        padding: var(--space-xs) var(--space-sm);
        border-radius: var(--radius-full);
        font-size: var(--font-size-xs);
        font-weight: var(--font-weight-semibold);
        text-transform: uppercase;
    }

    .status-active {
        background: var(--success-light);
        color: var(--success);
    }

    .status-inactive {
        background: var(--danger-light);
        color: var(--danger);
    }

    .add-department-btn {
        position: fixed;
        bottom: var(--space-2xl);
        right: var(--space-2xl);
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-blue), var(--secondary-teal));
        color: var(--white);
        border: none;
        font-size: var(--font-size-2xl);
        cursor: pointer;
        box-shadow: var(--shadow-lg);
        transition: var(--transition-normal);
        z-index: 1000;
    }

    .add-department-btn:hover {
        transform: scale(1.1);
        box-shadow: var(--shadow-xl);
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        25% { transform: translateY(-10px) rotate(5deg); }
        50% { transform: translateY(-5px) rotate(-5deg); }
        75% { transform: translateY(-15px) rotate(3deg); }
    }

    /* التصميم المتجاوب */
    @media (max-width: 768px) {
        .department-header {
            flex-direction: column;
            text-align: center;
        }

        .department-icon {
            width: 60px;
            height: 60px;
            font-size: var(--font-size-2xl);
        }

        .department-stats {
            justify-content: center;
        }

        .department-actions {
            flex-direction: column;
            gap: var(--space-md);
        }

        .action-buttons {
            width: 100%;
            justify-content: center;
        }

        .add-department-btn {
            bottom: var(--space-lg);
            right: var(--space-lg);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-professional">
    <!-- رأس الصفحة -->
    <div class="departments-header">
        <div class="container-professional">
            <div class="text-center">
                <div class="dashboard-icon">
                    <i class="fas fa-building"></i>
                </div>
                <h1 class="dashboard-title">إدارة الأقسام</h1>
                <p class="dashboard-subtitle">نظام شامل لإدارة جميع أقسام المنشأة</p>
            </div>
        </div>
    </div>

    <!-- إحصائيات الأقسام -->
    <div class="departments-stats" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--space-lg); margin-bottom: var(--space-2xl);">
        <div class="stat-card total" style="background: var(--white); border: var(--border-light); border-radius: var(--radius-xl); box-shadow: var(--shadow-sm); padding: var(--space-lg); text-align: center; transition: var(--transition-normal); position: relative; overflow: hidden;">
            <div class="stat-header" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--space-lg);">
                <div class="stat-icon" style="width: 50px; height: 50px; border-radius: var(--radius-xl); display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-light)); color: var(--white); font-size: var(--font-size-xl);">
                    <i class="fas fa-building"></i>
                </div>
                <div class="stat-value" style="font-size: var(--font-size-3xl); font-weight: var(--font-weight-bold); color: var(--primary-blue);">2</div>
            </div>
            <div class="stat-label" style="font-size: var(--font-size-base); font-weight: var(--font-weight-semibold); color: var(--near-black); margin-bottom: var(--space-xs);">إجمالي الأقسام</div>
            <div class="stat-description" style="font-size: var(--font-size-sm); color: var(--dark-gray);">أقسام نشطة</div>
        </div>

        <div class="stat-card employees" style="background: var(--white); border: var(--border-light); border-radius: var(--radius-xl); box-shadow: var(--shadow-sm); padding: var(--space-lg); text-align: center; transition: var(--transition-normal); position: relative; overflow: hidden;">
            <div class="stat-header" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--space-lg);">
                <div class="stat-icon" style="width: 50px; height: 50px; border-radius: var(--radius-xl); display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, var(--accent-green), var(--success)); color: var(--white); font-size: var(--font-size-xl);">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-value" style="font-size: var(--font-size-3xl); font-weight: var(--font-weight-bold); color: var(--accent-green);">43</div>
            </div>
            <div class="stat-label" style="font-size: var(--font-size-base); font-weight: var(--font-weight-semibold); color: var(--near-black); margin-bottom: var(--space-xs);">إجمالي الموظفين</div>
            <div class="stat-description" style="font-size: var(--font-size-sm); color: var(--dark-gray);">في جميع الأقسام</div>
        </div>

        <div class="stat-card managers" style="background: var(--white); border: var(--border-light); border-radius: var(--radius-xl); box-shadow: var(--shadow-sm); padding: var(--space-lg); text-align: center; transition: var(--transition-normal); position: relative; overflow: hidden;">
            <div class="stat-header" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--space-lg);">
                <div class="stat-icon" style="width: 50px; height: 50px; border-radius: var(--radius-xl); display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, var(--accent-purple), #8b5cf6); color: var(--white); font-size: var(--font-size-xl);">
                    <i class="fas fa-user-tie"></i>
                </div>
                <div class="stat-value" style="font-size: var(--font-size-3xl); font-weight: var(--font-weight-bold); color: var(--accent-purple);">5</div>
            </div>
            <div class="stat-label" style="font-size: var(--font-size-base); font-weight: var(--font-weight-semibold); color: var(--near-black); margin-bottom: var(--space-xs);">المدراء</div>
            <div class="stat-description" style="font-size: var(--font-size-sm); color: var(--dark-gray);">مدراء الأقسام</div>
        </div>

        <div class="stat-card attendance" style="background: var(--white); border: var(--border-light); border-radius: var(--radius-xl); box-shadow: var(--shadow-sm); padding: var(--space-lg); text-align: center; transition: var(--transition-normal); position: relative; overflow: hidden;">
            <div class="stat-header" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: var(--space-lg);">
                <div class="stat-icon" style="width: 50px; height: 50px; border-radius: var(--radius-xl); display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, var(--warning), var(--accent-orange)); color: var(--white); font-size: var(--font-size-xl);">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-value" style="font-size: var(--font-size-3xl); font-weight: var(--font-weight-bold); color: var(--warning);">96%</div>
            </div>
            <div class="stat-label" style="font-size: var(--font-size-base); font-weight: var(--font-weight-semibold); color: var(--near-black); margin-bottom: var(--space-xs);">معدل الحضور</div>
            <div class="stat-description" style="font-size: var(--font-size-sm); color: var(--dark-gray);">المتوسط العام</div>
        </div>
    </div>

    <!-- قائمة الأقسام -->
    <div class="row-responsive">
        <!-- قسم المختبر -->
        <div class="col-responsive col-lg-6">
            <div class="department-card lab">
                <div class="department-header">
                    <div class="department-icon">
                        <i class="fas fa-flask"></i>
                    </div>
                    <div class="department-info">
                        <h3>قسم المختبر</h3>
                        <p>قسم التحاليل الطبية والفحوصات المخبرية</p>
                        <div class="department-stats">
                            <div class="stat-item">
                                <span class="stat-number">25</span>
                                <div class="stat-label">موظف</div>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">3</span>
                                <div class="stat-label">مدراء</div>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">97%</span>
                                <div class="stat-label">الحضور</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="department-actions">
                    <div class="department-status status-active">
                        <i class="fas fa-check-circle"></i>
                        نشط
                    </div>
                    <div class="action-buttons">
                        <a href="/department/1" class="btn-department btn-view">
                            <i class="fas fa-eye"></i>
                            عرض
                        </a>
                        <a href="/department/1/edit" class="btn-department btn-edit">
                            <i class="fas fa-edit"></i>
                            تعديل
                        </a>
                        <button class="btn-department btn-delete" onclick="deleteDepartment(1)">
                            <i class="fas fa-trash"></i>
                            حذف
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم الأشعة -->
        <div class="col-responsive col-lg-6">
            <div class="department-card radiology">
                <div class="department-header">
                    <div class="department-icon">
                        <i class="fas fa-x-ray"></i>
                    </div>
                    <div class="department-info">
                        <h3>قسم الأشعة</h3>
                        <p>قسم التصوير الطبي والأشعة التشخيصية</p>
                        <div class="department-stats">
                            <div class="stat-item">
                                <span class="stat-number">18</span>
                                <div class="stat-label">موظف</div>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">2</span>
                                <div class="stat-label">مدراء</div>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">95%</span>
                                <div class="stat-label">الحضور</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="department-actions">
                    <div class="department-status status-active">
                        <i class="fas fa-check-circle"></i>
                        نشط
                    </div>
                    <div class="action-buttons">
                        <a href="/department/2" class="btn-department btn-view">
                            <i class="fas fa-eye"></i>
                            عرض
                        </a>
                        <a href="/department/2/edit" class="btn-department btn-edit">
                            <i class="fas fa-edit"></i>
                            تعديل
                        </a>
                        <button class="btn-department btn-delete" onclick="deleteDepartment(2)">
                            <i class="fas fa-trash"></i>
                            حذف
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- زر إضافة قسم جديد -->
    <button class="add-department-btn" onclick="showAddDepartmentModal()">
        <i class="fas fa-plus"></i>
    </button>
</div>

<!-- نافذة إضافة قسم جديد -->
<div class="modal fade" id="addDepartmentModal" tabindex="-1" aria-labelledby="addDepartmentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background: linear-gradient(135deg, var(--primary-blue), var(--secondary-teal)); color: white;">
                <h5 class="modal-title" id="addDepartmentModalLabel">
                    <i class="fas fa-plus-circle me-2"></i>
                    إضافة قسم جديد
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addDepartmentForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="departmentName" class="form-label">
                                <i class="fas fa-building me-1"></i>
                                اسم القسم
                            </label>
                            <input type="text" class="form-control" id="departmentName" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="departmentIcon" class="form-label">
                                <i class="fas fa-icons me-1"></i>
                                أيقونة القسم
                            </label>
                            <select class="form-select" id="departmentIcon" required>
                                <option value="">اختر أيقونة</option>
                                <option value="fas fa-flask">مختبر (flask)</option>
                                <option value="fas fa-x-ray">أشعة (x-ray)</option>
                                <option value="fas fa-heartbeat">قلب (heartbeat)</option>
                                <option value="fas fa-brain">مخ وأعصاب (brain)</option>
                                <option value="fas fa-eye">عيون (eye)</option>
                                <option value="fas fa-tooth">أسنان (tooth)</option>
                                <option value="fas fa-user-md">طبيب (user-md)</option>
                                <option value="fas fa-stethoscope">سماعة (stethoscope)</option>
                                <option value="fas fa-pills">صيدلية (pills)</option>
                                <option value="fas fa-ambulance">إسعاف (ambulance)</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="departmentDescription" class="form-label">
                            <i class="fas fa-align-left me-1"></i>
                            وصف القسم
                        </label>
                        <textarea class="form-control" id="departmentDescription" rows="3" required></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="departmentColor" class="form-label">
                                <i class="fas fa-palette me-1"></i>
                                لون القسم
                            </label>
                            <select class="form-select" id="departmentColor" required>
                                <option value="">اختر لون</option>
                                <option value="primary-blue" style="color: #2563eb;">أزرق أساسي</option>
                                <option value="secondary-teal" style="color: #0891b2;">تيل ثانوي</option>
                                <option value="accent-green" style="color: #059669;">أخضر</option>
                                <option value="accent-purple" style="color: #7c3aed;">بنفسجي</option>
                                <option value="warning" style="color: #d97706;">برتقالي</option>
                                <option value="accent-red" style="color: #dc2626;">أحمر</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="departmentManager" class="form-label">
                                <i class="fas fa-user-tie me-1"></i>
                                مدير القسم
                            </label>
                            <input type="text" class="form-control" id="departmentManager" placeholder="اختياري">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>
                    إلغاء
                </button>
                <button type="button" class="btn btn-primary" onclick="addDepartment()">
                    <i class="fas fa-plus me-1"></i>
                    إضافة القسم
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function showAddDepartmentModal() {
    const modal = new bootstrap.Modal(document.getElementById('addDepartmentModal'));
    modal.show();
}

function addDepartment() {
    const name = document.getElementById('departmentName').value;
    const icon = document.getElementById('departmentIcon').value;
    const description = document.getElementById('departmentDescription').value;
    const color = document.getElementById('departmentColor').value;
    const manager = document.getElementById('departmentManager').value;

    if (!name || !icon || !description || !color) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    // هنا يمكن إضافة منطق إرسال البيانات إلى الخادم
    console.log('إضافة قسم جديد:', {
        name: name,
        icon: icon,
        description: description,
        color: color,
        manager: manager
    });

    alert('تم إضافة القسم بنجاح!');

    // إغلاق النافذة وإعادة تعيين النموذج
    const modal = bootstrap.Modal.getInstance(document.getElementById('addDepartmentModal'));
    modal.hide();
    document.getElementById('addDepartmentForm').reset();

    // إعادة تحميل الصفحة لإظهار القسم الجديد
    location.reload();
}

function deleteDepartment(departmentId) {
    if (confirm('هل أنت متأكد من حذف هذا القسم؟ سيتم حذف جميع البيانات المرتبطة به.')) {
        // هنا يمكن إضافة منطق حذف القسم من الخادم
        console.log('حذف القسم رقم:', departmentId);
        alert('تم حذف القسم بنجاح!');
        location.reload();
    }
}
</script>
{% endblock %}
