-- ===== ALEMIS Database Optimizations =====
-- تحسينات قاعدة البيانات لنظام ALEMIS
-- Database Performance Optimizations

-- ===== إنشاء الفهارس للأداء =====
-- Performance Indexes

-- فهارس جدول المستخدمين
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_department_id ON users(department_id);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);
CREATE INDEX IF NOT EXISTS idx_users_last_login ON users(last_login);

-- فهارس جدول طلبات الإجازة
CREATE INDEX IF NOT EXISTS idx_leave_requests_user_id ON leave_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_leave_requests_leave_type_id ON leave_requests(leave_type_id);
CREATE INDEX IF NOT EXISTS idx_leave_requests_status ON leave_requests(status);
CREATE INDEX IF NOT EXISTS idx_leave_requests_start_date ON leave_requests(start_date);
CREATE INDEX IF NOT EXISTS idx_leave_requests_end_date ON leave_requests(end_date);
CREATE INDEX IF NOT EXISTS idx_leave_requests_created_at ON leave_requests(created_at);
CREATE INDEX IF NOT EXISTS idx_leave_requests_date_range ON leave_requests(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_leave_requests_user_status ON leave_requests(user_id, status);

-- فهارس جدول أرصدة الإجازات
CREATE INDEX IF NOT EXISTS idx_leave_balances_user_id ON leave_balances(user_id);
CREATE INDEX IF NOT EXISTS idx_leave_balances_leave_type_id ON leave_balances(leave_type_id);
CREATE INDEX IF NOT EXISTS idx_leave_balances_year ON leave_balances(year);
CREATE INDEX IF NOT EXISTS idx_leave_balances_user_year ON leave_balances(user_id, year);

-- فهارس جدول طلبات التغطية
CREATE INDEX IF NOT EXISTS idx_coverage_requests_user_id ON coverage_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_coverage_requests_status ON coverage_requests(status);
CREATE INDEX IF NOT EXISTS idx_coverage_requests_coverage_date ON coverage_requests(coverage_date);
CREATE INDEX IF NOT EXISTS idx_coverage_requests_created_at ON coverage_requests(created_at);

-- فهارس جدول طلبات تبديل الدوام
CREATE INDEX IF NOT EXISTS idx_shift_swap_requests_user_id ON shift_swap_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_shift_swap_requests_swap_with_user_id ON shift_swap_requests(swap_with_user_id);
CREATE INDEX IF NOT EXISTS idx_shift_swap_requests_status ON shift_swap_requests(status);
CREATE INDEX IF NOT EXISTS idx_shift_swap_requests_created_at ON shift_swap_requests(created_at);

-- فهارس جدول الجداول الشهرية
CREATE INDEX IF NOT EXISTS idx_monthly_schedules_month ON monthly_schedules(month);
CREATE INDEX IF NOT EXISTS idx_monthly_schedules_year ON monthly_schedules(year);
CREATE INDEX IF NOT EXISTS idx_monthly_schedules_department_id ON monthly_schedules(department_id);
CREATE INDEX IF NOT EXISTS idx_monthly_schedules_status ON monthly_schedules(status);

-- فهارس جدول تعيينات الجدولة
CREATE INDEX IF NOT EXISTS idx_schedule_assignments_user_id ON schedule_assignments(user_id);
CREATE INDEX IF NOT EXISTS idx_schedule_assignments_schedule_id ON schedule_assignments(schedule_id);
CREATE INDEX IF NOT EXISTS idx_schedule_assignments_date ON schedule_assignments(date);
CREATE INDEX IF NOT EXISTS idx_schedule_assignments_shift_type ON schedule_assignments(shift_type);
CREATE INDEX IF NOT EXISTS idx_schedule_assignments_user_date ON schedule_assignments(user_id, date);

-- فهارس جدول سجل التدقيق
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_entity_type ON audit_logs(entity_type);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at);

-- فهارس جدول طلبات تعديل الملف الشخصي
CREATE INDEX IF NOT EXISTS idx_profile_change_requests_user_id ON profile_change_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_profile_change_requests_status ON profile_change_requests(status);
CREATE INDEX IF NOT EXISTS idx_profile_change_requests_request_type ON profile_change_requests(request_type);
CREATE INDEX IF NOT EXISTS idx_profile_change_requests_created_at ON profile_change_requests(created_at);

-- ===== Views للاستعلامات المعقدة =====
-- Complex Query Views

-- عرض ملخص الموظفين
CREATE VIEW IF NOT EXISTS employee_summary AS
SELECT 
    u.id,
    u.username,
    u.first_name || ' ' || u.last_name as full_name,
    u.email,
    u.role,
    d.name as department_name,
    u.is_active,
    u.created_at,
    u.last_login,
    COUNT(DISTINCT lr.id) as total_leave_requests,
    COUNT(DISTINCT CASE WHEN lr.status = 'approved' THEN lr.id END) as approved_leaves,
    COUNT(DISTINCT cr.id) as coverage_requests,
    COUNT(DISTINCT ssr.id) as shift_swap_requests
FROM users u
LEFT JOIN departments d ON u.department_id = d.id
LEFT JOIN leave_requests lr ON u.id = lr.user_id
LEFT JOIN coverage_requests cr ON u.id = cr.user_id
LEFT JOIN shift_swap_requests ssr ON u.id = ssr.user_id
WHERE u.role = 'employee'
GROUP BY u.id;

-- عرض إحصائيات الإجازات الشهرية
CREATE VIEW IF NOT EXISTS monthly_leave_stats AS
SELECT 
    strftime('%Y-%m', lr.start_date) as month,
    lt.name as leave_type,
    COUNT(lr.id) as total_requests,
    COUNT(CASE WHEN lr.status = 'approved' THEN 1 END) as approved_requests,
    COUNT(CASE WHEN lr.status = 'rejected' THEN 1 END) as rejected_requests,
    COUNT(CASE WHEN lr.status = 'pending' THEN 1 END) as pending_requests,
    AVG(CASE WHEN lr.status = 'approved' THEN 
        julianday(lr.end_date) - julianday(lr.start_date) + 1 
        ELSE NULL END) as avg_duration
FROM leave_requests lr
JOIN leave_types lt ON lr.leave_type_id = lt.id
WHERE lr.start_date IS NOT NULL
GROUP BY strftime('%Y-%m', lr.start_date), lt.id
ORDER BY month DESC, total_requests DESC;

-- عرض إحصائيات الأقسام
CREATE VIEW IF NOT EXISTS department_stats AS
SELECT 
    d.id,
    d.name as department_name,
    COUNT(DISTINCT u.id) as total_employees,
    COUNT(DISTINCT CASE WHEN u.is_active = 1 THEN u.id END) as active_employees,
    COUNT(DISTINCT lr.id) as total_leave_requests,
    COUNT(DISTINCT CASE WHEN lr.status = 'approved' THEN lr.id END) as approved_leaves,
    COUNT(DISTINCT cr.id) as coverage_requests,
    COUNT(DISTINCT ssr.id) as shift_swaps,
    AVG(CASE WHEN lr.status = 'approved' THEN 
        julianday(lr.end_date) - julianday(lr.start_date) + 1 
        ELSE NULL END) as avg_leave_duration
FROM departments d
LEFT JOIN users u ON d.id = u.department_id
LEFT JOIN leave_requests lr ON u.id = lr.user_id
LEFT JOIN coverage_requests cr ON u.id = cr.user_id
LEFT JOIN shift_swap_requests ssr ON u.id = ssr.user_id
WHERE d.is_active = 1
GROUP BY d.id;

-- ===== Triggers للتحديث التلقائي =====
-- Automatic Update Triggers

-- تحديث أرصدة الإجازات عند الموافقة على طلب
CREATE TRIGGER IF NOT EXISTS update_leave_balance_on_approval
AFTER UPDATE OF status ON leave_requests
WHEN NEW.status = 'approved' AND OLD.status != 'approved'
BEGIN
    UPDATE leave_balances 
    SET used_days = used_days + (julianday(NEW.end_date) - julianday(NEW.start_date) + 1),
        remaining_days = remaining_days - (julianday(NEW.end_date) - julianday(NEW.start_date) + 1)
    WHERE user_id = NEW.user_id 
    AND leave_type_id = NEW.leave_type_id 
    AND year = strftime('%Y', NEW.start_date);
END;

-- إضافة سجل تدقيق عند تعديل البيانات الحساسة
CREATE TRIGGER IF NOT EXISTS audit_user_changes
AFTER UPDATE ON users
FOR EACH ROW
WHEN OLD.username != NEW.username 
    OR OLD.email != NEW.email 
    OR OLD.role != NEW.role 
    OR OLD.is_active != NEW.is_active
BEGIN
    INSERT INTO audit_logs (user_id, action, entity_type, entity_id, details, created_at)
    VALUES (NEW.id, 'update_user', 'user', NEW.id, 
            'تم تعديل بيانات المستخدم: ' || NEW.username, datetime('now'));
END;

-- تحديث آخر نشاط للمستخدم
CREATE TRIGGER IF NOT EXISTS update_last_activity
AFTER INSERT ON audit_logs
FOR EACH ROW
WHEN NEW.user_id IS NOT NULL
BEGIN
    UPDATE users 
    SET last_activity = datetime('now')
    WHERE id = NEW.user_id;
END;

-- ===== إجراءات التنظيف =====
-- Cleanup Procedures

-- حذف السجلات القديمة (يجب تشغيلها دورياً)
-- DELETE FROM audit_logs WHERE created_at < datetime('now', '-1 year');
-- DELETE FROM notifications WHERE created_at < datetime('now', '-3 months') AND is_read = 1;

-- ===== إحصائيات الأداء =====
-- Performance Statistics

-- تحليل استخدام الفهارس
-- PRAGMA index_list('table_name');
-- PRAGMA index_info('index_name');

-- إحصائيات الجداول
-- SELECT name, COUNT(*) as row_count FROM sqlite_master WHERE type='table' GROUP BY name;

-- ===== نصائح التحسين =====
-- Optimization Tips

-- 1. استخدم EXPLAIN QUERY PLAN لتحليل الاستعلامات
-- 2. تأكد من وجود فهارس على الأعمدة المستخدمة في WHERE و JOIN
-- 3. استخدم LIMIT في الاستعلامات الكبيرة
-- 4. فكر في تقسيم الجداول الكبيرة
-- 5. استخدم Views للاستعلامات المعقدة المتكررة
-- 6. نظف البيانات القديمة دورياً
-- 7. استخدم المعاملات للعمليات المتعددة
-- 8. فكر في استخدام قاعدة بيانات أكثر تقدماً مثل PostgreSQL للمشاريع الكبيرة

-- ===== إعدادات SQLite للأداء =====
-- SQLite Performance Settings

-- PRAGMA journal_mode = WAL;
-- PRAGMA synchronous = NORMAL;
-- PRAGMA cache_size = 10000;
-- PRAGMA temp_store = MEMORY;
-- PRAGMA mmap_size = 268435456; -- 256MB
