# 🚀 دليل المطور - ALEMIS Enhanced

## نظام إدارة إجازات الموظفين في المختبرات المحسن
**Enhanced Laboratory Employee Leave Management System**

---

## 📋 جدول المحتويات

- [نظرة عامة](#نظرة-عامة)
- [البنية التقنية](#البنية-التقنية)
- [إعداد بيئة التطوير](#إعداد-بيئة-التطوير)
- [هيكل المشروع](#هيكل-المشروع)
- [الميزات المحسنة](#الميزات-المحسنة)
- [أفضل الممارسات](#أفضل-الممارسات)
- [الاختبارات](#الاختبارات)
- [النشر](#النشر)
- [استكشاف الأخطاء](#استكشاف-الأخطاء)

---

## 🎯 نظرة عامة

ALEMIS Enhanced هو نظام شامل ومتطور لإدارة إجازات الموظفين في المختبرات، مبني بأحدث التقنيات وأفضل الممارسات.

### الميزات الرئيسية:
- ✅ **أمان متقدم**: تشفير البيانات، مصادقة ثنائية، حماية CSRF
- ✅ **أداء محسن**: تخزين مؤقت ذكي، فهرسة قاعدة البيانات
- ✅ **واجهة حديثة**: تصميم متجاوب، مكونات تفاعلية
- ✅ **تقارير تفاعلية**: رسوم بيانية، تحليلات متقدمة
- ✅ **قابلية التوسع**: بنية معيارية، دعم Docker

---

## 🏗️ البنية التقنية

### Backend Stack:
```
Flask 3.0+          # إطار العمل الرئيسي
SQLAlchemy 2.0+     # ORM متقدم
Redis               # التخزين المؤقت
Celery              # المهام الخلفية
PostgreSQL/SQLite   # قاعدة البيانات
```

### Frontend Stack:
```
Jinja2              # قوالب HTML
Bootstrap 5 RTL     # إطار CSS
JavaScript ES6+     # التفاعل
Chart.js            # الرسوم البيانية
```

### Security & Performance:
```
bcrypt              # تشفير كلمات المرور
cryptography        # تشفير البيانات
Flask-Limiter       # تحديد المعدل
Flask-Talisman      # رؤوس الأمان
```

---

## ⚙️ إعداد بيئة التطوير

### 1. متطلبات النظام:
```bash
Python 3.8+
Node.js 16+ (اختياري)
Redis Server (اختياري)
Git
```

### 2. التثبيت السريع:
```bash
# نسخ المشروع
git clone <repository-url>
cd alemis

# إنشاء بيئة افتراضية
python -m venv venv
source venv/bin/activate  # Linux/Mac
# أو
venv\Scripts\activate     # Windows

# تثبيت التبعيات الأساسية
pip install -r requirements-minimal.txt

# أو التثبيت الكامل
pip install -r requirements.txt

# تشغيل التطبيق
python app.py
```

### 3. إعداد قاعدة البيانات:
```bash
# تطبيق تحسينات قاعدة البيانات
sqlite3 alemis.db < database_optimizations.sql

# أو باستخدام Python
python -c "
from app import create_app
app = create_app()
with app.app_context():
    # تهيئة قاعدة البيانات
    pass
"
```

---

## 📁 هيكل المشروع

```
alemis/
├── app/                          # الوحدات الأساسية
│   ├── __init__.py
│   ├── models.py                 # نماذج قاعدة البيانات
│   ├── database.py               # إدارة قاعدة البيانات
│   ├── security_enhanced.py      # نظام الأمان المحسن
│   ├── cache.py                  # نظام التخزين المؤقت
│   ├── reports_enhanced.py       # التقارير التفاعلية
│   ├── notifications.py          # نظام الإشعارات
│   └── api.py                    # واجهة برمجة التطبيقات
├── static/                       # الملفات الثابتة
│   ├── css/
│   │   ├── style.css            # الأنماط الأساسية
│   │   ├── enhanced-ui.css      # الأنماط المحسنة
│   │   └── dark-mode.css        # الوضع المظلم
│   ├── js/
│   │   ├── enhanced-ui.js       # JavaScript محسن
│   │   └── professional.js      # وظائف متقدمة
│   └── images/                  # الصور والأيقونات
├── templates/                    # قوالب HTML
│   ├── base.html                # القالب الأساسي
│   ├── dashboard.html           # لوحة التحكم
│   └── ...                      # باقي القوالب
├── tests/                       # الاختبارات
│   ├── test_enhanced_features.py
│   └── ...
├── requirements.txt             # التبعيات الكاملة
├── requirements-minimal.txt     # التبعيات الأساسية
├── database_optimizations.sql  # تحسينات قاعدة البيانات
├── app.py                       # نقطة الدخول الرئيسية
├── config.py                    # إعدادات التطبيق
└── README.md                    # دليل المستخدم
```

---

## 🌟 الميزات المحسنة

### 1. نظام الأمان المتقدم:
```python
from app.security_enhanced import SecurityManager

# تشفير كلمات المرور
security = SecurityManager()
hashed = security.hash_password("password123")

# تشفير البيانات الحساسة
encrypted = security.encrypt_data("sensitive_data")

# المصادقة الثنائية
secret_data = security.generate_2fa_secret("<EMAIL>")
```

### 2. التقارير التفاعلية:
```python
from app.reports_enhanced import EnhancedReportsManager, ReportType, ReportFilter

# إنشاء مرشح التقرير
filters = ReportFilter(
    start_date=date(2024, 1, 1),
    end_date=date(2024, 12, 31),
    department_ids=[1, 2]
)

# إنتاج التقرير
reports = EnhancedReportsManager()
report = reports.generate_report(ReportType.LEAVE_SUMMARY, filters)
```

### 3. التخزين المؤقت الذكي:
```python
from app.cache import CacheManager

cache = CacheManager()

# تخزين البيانات
cache.set("key", "value", timeout=300)

# استرجاع البيانات
value = cache.get("key")
```

---

## 📝 أفضل الممارسات

### 1. كتابة الكود:
- استخدم Type Hints في Python
- اتبع PEP 8 للتنسيق
- اكتب docstrings للدوال والكلاسات
- استخدم أسماء متغيرات واضحة

### 2. الأمان:
- لا تحفظ كلمات المرور في النص الخام
- استخدم HTTPS في الإنتاج
- فعل CSRF protection
- تحقق من صحة جميع المدخلات

### 3. الأداء:
- استخدم الفهارس في قاعدة البيانات
- طبق التخزين المؤقت للاستعلامات الثقيلة
- استخدم pagination للقوائم الطويلة
- ضغط الملفات الثابتة

### 4. قاعدة البيانات:
```sql
-- استخدم الفهارس للأعمدة المستخدمة في WHERE
CREATE INDEX idx_users_email ON users(email);

-- استخدم Views للاستعلامات المعقدة
CREATE VIEW employee_summary AS
SELECT u.*, d.name as department_name
FROM users u JOIN departments d ON u.department_id = d.id;
```

---

## 🧪 الاختبارات

### تشغيل الاختبارات:
```bash
# تشغيل جميع الاختبارات
pytest

# تشغيل اختبارات محددة
pytest tests/test_enhanced_features.py

# تشغيل مع تقرير التغطية
pytest --cov=app --cov-report=html

# تشغيل الاختبارات السريعة فقط
pytest -m "not slow"
```

### كتابة اختبارات جديدة:
```python
import pytest
from app.security_enhanced import SecurityManager

class TestNewFeature:
    @pytest.fixture
    def security_manager(self):
        return SecurityManager()
    
    def test_new_functionality(self, security_manager):
        # اختبار الوظيفة الجديدة
        result = security_manager.new_method()
        assert result is not None
```

---

## 🚀 النشر

### 1. النشر باستخدام Docker:
```bash
# بناء الصورة
docker build -t alemis:latest .

# تشغيل الحاوية
docker run -p 5000:5000 alemis:latest

# أو باستخدام Docker Compose
docker-compose up -d
```

### 2. النشر التقليدي:
```bash
# تثبيت خادم الإنتاج
pip install gunicorn

# تشغيل التطبيق
gunicorn --bind 0.0.0.0:5000 --workers 4 app:app
```

### 3. متغيرات البيئة المهمة:
```bash
export FLASK_ENV=production
export SECRET_KEY=your-secret-key
export DATABASE_URL=postgresql://user:pass@localhost/alemis
export REDIS_URL=redis://localhost:6379/0
```

---

## 🔧 استكشاف الأخطاء

### مشاكل شائعة:

#### 1. خطأ في قاعدة البيانات:
```bash
# حذف قاعدة البيانات وإعادة إنشائها
rm alemis.db
python -c "from app import create_app; app = create_app(); app.app_context().push()"
```

#### 2. مشاكل التبعيات:
```bash
# تحديث pip
pip install --upgrade pip

# إعادة تثبيت التبعيات
pip install -r requirements.txt --force-reinstall
```

#### 3. مشاكل الأداء:
```sql
-- فحص الاستعلامات البطيئة
EXPLAIN QUERY PLAN SELECT * FROM users WHERE email = '<EMAIL>';

-- إضافة فهارس مفقودة
CREATE INDEX idx_missing ON table_name(column_name);
```

---

## 📞 الدعم والمساهمة

### الحصول على المساعدة:
- راجع [دليل المستخدم](README.md)
- اطلع على [دليل المساهمة](CONTRIBUTING.md)
- تحقق من [سجل التغييرات](CHANGELOG.md)

### المساهمة في المشروع:
1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. كتابة الاختبارات
4. إرسال Pull Request

---

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

**تم تطوير هذا النظام بعناية فائقة لضمان الجودة والأمان والأداء العالي** 🚀
