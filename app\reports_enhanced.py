"""
نظام التقارير التفاعلية المحسن
Enhanced Interactive Reports System
"""
import json
import logging
from datetime import datetime, timedelta, date
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

# استيراد اختياري للمكتبات المتقدمة
try:
    import pandas as pd
    import numpy as np
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

try:
    from flask import current_app, request, jsonify
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False

try:
    from sqlalchemy import text, func
    from sqlalchemy.orm import Session
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    SQLALCHEMY_AVAILABLE = False

logger = logging.getLogger(__name__)


class ReportsManager:
    """مدير التقارير المحسن"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def get_dashboard_stats(self):
        """الحصول على إحصائيات لوحة التحكم"""
        try:
            # إحصائيات أساسية (يمكن تحسينها لاحقاً)
            return {
                'total_employees': 25,
                'on_leave_today': 3,
                'pending_requests': 7,
                'monthly_requests': 45,
                'approved_requests': 38,
                'rejected_requests': 2,
                'coverage_requests': 12,
                'shift_swaps': 8
            }
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على إحصائيات لوحة التحكم: {e}")
            return {
                'total_employees': 0,
                'on_leave_today': 0,
                'pending_requests': 0,
                'monthly_requests': 0
            }

    def get_dashboard_charts(self):
        """الحصول على بيانات الرسوم البيانية للوحة التحكم"""
        try:
            return {
                'leave_trends': {
                    'labels': ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                    'data': [12, 19, 15, 25, 22, 18]
                },
                'department_distribution': {
                    'labels': ['المختبر الرئيسي', 'الكيمياء الحيوية', 'الأحياء الدقيقة', 'أمراض الدم'],
                    'data': [8, 6, 5, 6]
                },
                'request_status': {
                    'labels': ['معتمد', 'قيد المراجعة', 'مرفوض'],
                    'data': [38, 7, 2]
                }
            }
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على بيانات الرسوم البيانية: {e}")
            return {}

    def get_recent_activities(self, limit=10):
        """الحصول على الأنشطة الحديثة"""
        try:
            # أنشطة تجريبية (يمكن ربطها بقاعدة البيانات لاحقاً)
            activities = [
                {
                    'id': 1,
                    'user': 'أحمد محمد',
                    'action': 'طلب إجازة سنوية',
                    'timestamp': datetime.now() - timedelta(minutes=15),
                    'status': 'pending'
                },
                {
                    'id': 2,
                    'user': 'فاطمة علي',
                    'action': 'طلب تبديل دوام',
                    'timestamp': datetime.now() - timedelta(hours=2),
                    'status': 'approved'
                },
                {
                    'id': 3,
                    'user': 'محمد سالم',
                    'action': 'طلب تغطية',
                    'timestamp': datetime.now() - timedelta(hours=4),
                    'status': 'pending'
                }
            ]
            return activities[:limit]
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على الأنشطة الحديثة: {e}")
            return []

    def get_all_reports(self):
        """الحصول على جميع التقارير"""
        try:
            return {
                'monthly_summary': self._get_monthly_summary(),
                'department_reports': self._get_department_reports(),
                'employee_reports': self._get_employee_reports(),
                'leave_analysis': self._get_leave_analysis()
            }
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على التقارير: {e}")
            return {}

    def get_interactive_reports(self):
        """الحصول على التقارير التفاعلية"""
        try:
            return {
                'charts': self.get_dashboard_charts(),
                'filters': self._get_available_filters(),
                'export_options': self._get_export_options()
            }
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على التقارير التفاعلية: {e}")
            return {}

    def _get_monthly_summary(self):
        """ملخص شهري"""
        return {
            'total_requests': 45,
            'approved': 38,
            'pending': 7,
            'rejected': 2
        }

    def _get_department_reports(self):
        """تقارير الأقسام"""
        return [
            {'name': 'المختبر الرئيسي', 'employees': 8, 'requests': 15},
            {'name': 'الكيمياء الحيوية', 'employees': 6, 'requests': 12},
            {'name': 'الأحياء الدقيقة', 'employees': 5, 'requests': 10},
            {'name': 'أمراض الدم', 'employees': 6, 'requests': 8}
        ]

    def _get_employee_reports(self):
        """تقارير الموظفين"""
        return [
            {'name': 'أحمد محمد', 'department': 'المختبر الرئيسي', 'requests': 3},
            {'name': 'فاطمة علي', 'department': 'الكيمياء الحيوية', 'requests': 2},
            {'name': 'محمد سالم', 'department': 'الأحياء الدقيقة', 'requests': 4}
        ]

    def _get_leave_analysis(self):
        """تحليل الإجازات"""
        return {
            'most_requested_type': 'إجازة سنوية',
            'peak_months': ['يونيو', 'يوليو', 'أغسطس'],
            'average_duration': 5.2
        }

    def _get_available_filters(self):
        """المرشحات المتاحة"""
        return {
            'date_ranges': ['هذا الشهر', 'الشهر الماضي', 'آخر 3 أشهر', 'هذا العام'],
            'departments': ['جميع الأقسام', 'المختبر الرئيسي', 'الكيمياء الحيوية', 'الأحياء الدقيقة'],
            'request_types': ['جميع الأنواع', 'إجازة سنوية', 'إجازة مرضية', 'تبديل دوام']
        }

    def _get_export_options(self):
        """خيارات التصدير"""
        return ['PDF', 'Excel', 'CSV', 'JSON']


class ReportType(Enum):
    """أنواع التقارير"""
    LEAVE_SUMMARY = "leave_summary"
    EMPLOYEE_PERFORMANCE = "employee_performance"
    DEPARTMENT_ANALYTICS = "department_analytics"
    ATTENDANCE_REPORT = "attendance_report"
    COVERAGE_ANALYSIS = "coverage_analysis"
    SHIFT_PATTERNS = "shift_patterns"
    MONTHLY_OVERVIEW = "monthly_overview"
    YEARLY_TRENDS = "yearly_trends"


@dataclass
class ReportFilter:
    """مرشح التقارير"""
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    department_ids: Optional[List[int]] = None
    user_ids: Optional[List[int]] = None
    leave_types: Optional[List[str]] = None
    status: Optional[str] = None
    shift_types: Optional[List[str]] = None


class EnhancedReportsManager:
    """مدير التقارير التفاعلية المحسن"""
    
    def __init__(self, db_session=None):
        self.db_session = db_session
        self.cache_timeout = 300  # 5 دقائق
    
    def generate_report(self, report_type: ReportType, filters: ReportFilter) -> Dict[str, Any]:
        """إنتاج التقرير المطلوب"""
        try:
            report_method = getattr(self, f"_generate_{report_type.value}")
            return report_method(filters)
        except AttributeError:
            logger.error(f"نوع التقرير غير مدعوم: {report_type.value}")
            return {"error": "نوع التقرير غير مدعوم"}
        except Exception as e:
            logger.error(f"خطأ في إنتاج التقرير: {e}")
            return {"error": "فشل في إنتاج التقرير"}
    
    def _generate_leave_summary(self, filters: ReportFilter) -> Dict[str, Any]:
        """تقرير ملخص الإجازات"""
        if not SQLALCHEMY_AVAILABLE or not self.db_session:
            return self._generate_mock_leave_summary()
        
        # استعلام قاعدة البيانات
        query = """
        SELECT 
            lt.name as leave_type,
            COUNT(lr.id) as total_requests,
            SUM(CASE WHEN lr.status = 'approved' THEN 1 ELSE 0 END) as approved_requests,
            SUM(CASE WHEN lr.status = 'rejected' THEN 1 ELSE 0 END) as rejected_requests,
            SUM(CASE WHEN lr.status = 'pending' THEN 1 ELSE 0 END) as pending_requests,
            AVG(JULIANDAY(lr.end_date) - JULIANDAY(lr.start_date) + 1) as avg_duration
        FROM leave_requests lr
        JOIN leave_types lt ON lr.leave_type_id = lt.id
        WHERE 1=1
        """
        
        params = {}
        if filters.start_date:
            query += " AND lr.start_date >= :start_date"
            params['start_date'] = filters.start_date
        
        if filters.end_date:
            query += " AND lr.end_date <= :end_date"
            params['end_date'] = filters.end_date
        
        if filters.department_ids:
            query += " AND lr.user_id IN (SELECT id FROM users WHERE department_id IN :dept_ids)"
            params['dept_ids'] = tuple(filters.department_ids)
        
        query += " GROUP BY lt.id, lt.name ORDER BY total_requests DESC"
        
        try:
            result = self.db_session.execute(text(query), params)
            data = [dict(row) for row in result]
            
            return {
                "title": "ملخص الإجازات",
                "data": data,
                "summary": self._calculate_leave_summary_stats(data),
                "chart_data": self._prepare_leave_chart_data(data),
                "generated_at": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"خطأ في استعلام ملخص الإجازات: {e}")
            return self._generate_mock_leave_summary()
    
    def _generate_employee_performance(self, filters: ReportFilter) -> Dict[str, Any]:
        """تقرير أداء الموظفين"""
        if not SQLALCHEMY_AVAILABLE or not self.db_session:
            return self._generate_mock_employee_performance()
        
        query = """
        SELECT 
            u.first_name || ' ' || u.last_name as employee_name,
            u.employee_id,
            d.name as department,
            COUNT(lr.id) as total_leave_requests,
            SUM(CASE WHEN lr.status = 'approved' THEN 
                JULIANDAY(lr.end_date) - JULIANDAY(lr.start_date) + 1 
                ELSE 0 END) as total_leave_days,
            COUNT(cr.id) as coverage_requests,
            COUNT(ssr.id) as shift_swap_requests,
            AVG(CASE WHEN lr.status = 'approved' THEN 
                JULIANDAY(lr.end_date) - JULIANDAY(lr.start_date) + 1 
                ELSE NULL END) as avg_leave_duration
        FROM users u
        LEFT JOIN departments d ON u.department_id = d.id
        LEFT JOIN leave_requests lr ON u.id = lr.user_id
        LEFT JOIN coverage_requests cr ON u.id = cr.user_id
        LEFT JOIN shift_swap_requests ssr ON u.id = ssr.user_id
        WHERE u.role = 'employee' AND u.is_active = 1
        """
        
        params = {}
        if filters.start_date:
            query += " AND (lr.start_date IS NULL OR lr.start_date >= :start_date)"
            params['start_date'] = filters.start_date
        
        if filters.end_date:
            query += " AND (lr.end_date IS NULL OR lr.end_date <= :end_date)"
            params['end_date'] = filters.end_date
        
        if filters.department_ids:
            query += " AND u.department_id IN :dept_ids"
            params['dept_ids'] = tuple(filters.department_ids)
        
        query += " GROUP BY u.id ORDER BY total_leave_days DESC"
        
        try:
            result = self.db_session.execute(text(query), params)
            data = [dict(row) for row in result]
            
            return {
                "title": "تقرير أداء الموظفين",
                "data": data,
                "summary": self._calculate_performance_stats(data),
                "chart_data": self._prepare_performance_chart_data(data),
                "generated_at": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"خطأ في استعلام أداء الموظفين: {e}")
            return self._generate_mock_employee_performance()
    
    def _generate_department_analytics(self, filters: ReportFilter) -> Dict[str, Any]:
        """تحليلات الأقسام"""
        if not SQLALCHEMY_AVAILABLE or not self.db_session:
            return self._generate_mock_department_analytics()
        
        query = """
        SELECT 
            d.name as department_name,
            COUNT(DISTINCT u.id) as total_employees,
            COUNT(lr.id) as total_leave_requests,
            SUM(CASE WHEN lr.status = 'approved' THEN 1 ELSE 0 END) as approved_leaves,
            AVG(CASE WHEN lr.status = 'approved' THEN 
                JULIANDAY(lr.end_date) - JULIANDAY(lr.start_date) + 1 
                ELSE NULL END) as avg_leave_duration,
            COUNT(cr.id) as coverage_requests,
            COUNT(ssr.id) as shift_swaps
        FROM departments d
        LEFT JOIN users u ON d.id = u.department_id AND u.is_active = 1
        LEFT JOIN leave_requests lr ON u.id = lr.user_id
        LEFT JOIN coverage_requests cr ON u.id = cr.user_id
        LEFT JOIN shift_swap_requests ssr ON u.id = ssr.user_id
        WHERE d.is_active = 1
        """
        
        params = {}
        if filters.start_date:
            query += " AND (lr.start_date IS NULL OR lr.start_date >= :start_date)"
            params['start_date'] = filters.start_date
        
        if filters.end_date:
            query += " AND (lr.end_date IS NULL OR lr.end_date <= :end_date)"
            params['end_date'] = filters.end_date
        
        query += " GROUP BY d.id ORDER BY total_employees DESC"
        
        try:
            result = self.db_session.execute(text(query), params)
            data = [dict(row) for row in result]
            
            return {
                "title": "تحليلات الأقسام",
                "data": data,
                "summary": self._calculate_department_stats(data),
                "chart_data": self._prepare_department_chart_data(data),
                "generated_at": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"خطأ في استعلام تحليلات الأقسام: {e}")
            return self._generate_mock_department_analytics()
    
    def _generate_monthly_overview(self, filters: ReportFilter) -> Dict[str, Any]:
        """نظرة عامة شهرية"""
        current_month = filters.start_date or date.today().replace(day=1)
        
        # بيانات تجريبية للعرض
        return {
            "title": f"نظرة عامة - {current_month.strftime('%B %Y')}",
            "data": {
                "total_employees": 45,
                "on_leave_today": 8,
                "pending_requests": 12,
                "approved_requests": 156,
                "coverage_requests": 23,
                "shift_swaps": 34
            },
            "trends": {
                "leave_requests": [12, 15, 18, 14, 16, 19, 22, 18, 15, 17, 20, 16],
                "coverage_requests": [3, 4, 2, 5, 3, 6, 4, 3, 5, 4, 6, 3],
                "shift_swaps": [2, 3, 4, 2, 3, 5, 3, 4, 2, 3, 4, 5]
            },
            "generated_at": datetime.now().isoformat()
        }
    
    def _calculate_leave_summary_stats(self, data: List[Dict]) -> Dict[str, Any]:
        """حساب إحصائيات ملخص الإجازات"""
        if not data:
            return {}
        
        total_requests = sum(row['total_requests'] for row in data)
        total_approved = sum(row['approved_requests'] for row in data)
        
        return {
            "total_requests": total_requests,
            "total_approved": total_approved,
            "approval_rate": (total_approved / total_requests * 100) if total_requests > 0 else 0,
            "most_requested_type": max(data, key=lambda x: x['total_requests'])['leave_type'] if data else None
        }
    
    def _prepare_leave_chart_data(self, data: List[Dict]) -> Dict[str, Any]:
        """تحضير بيانات الرسم البياني للإجازات"""
        return {
            "pie_chart": {
                "labels": [row['leave_type'] for row in data],
                "data": [row['total_requests'] for row in data]
            },
            "bar_chart": {
                "labels": [row['leave_type'] for row in data],
                "datasets": [
                    {
                        "label": "موافق عليها",
                        "data": [row['approved_requests'] for row in data],
                        "backgroundColor": "#10b981"
                    },
                    {
                        "label": "مرفوضة",
                        "data": [row['rejected_requests'] for row in data],
                        "backgroundColor": "#ef4444"
                    },
                    {
                        "label": "قيد الانتظار",
                        "data": [row['pending_requests'] for row in data],
                        "backgroundColor": "#f59e0b"
                    }
                ]
            }
        }
    
    def _generate_mock_leave_summary(self) -> Dict[str, Any]:
        """بيانات تجريبية لملخص الإجازات"""
        mock_data = [
            {"leave_type": "إجازة اعتيادية", "total_requests": 45, "approved_requests": 38, "rejected_requests": 3, "pending_requests": 4, "avg_duration": 7.2},
            {"leave_type": "إجازة مرضية", "total_requests": 23, "approved_requests": 21, "rejected_requests": 1, "pending_requests": 1, "avg_duration": 3.5},
            {"leave_type": "إجازة طارئة", "total_requests": 12, "approved_requests": 10, "rejected_requests": 1, "pending_requests": 1, "avg_duration": 2.1},
            {"leave_type": "إجازة أمومة", "total_requests": 3, "approved_requests": 3, "rejected_requests": 0, "pending_requests": 0, "avg_duration": 90.0}
        ]
        
        return {
            "title": "ملخص الإجازات",
            "data": mock_data,
            "summary": self._calculate_leave_summary_stats(mock_data),
            "chart_data": self._prepare_leave_chart_data(mock_data),
            "generated_at": datetime.now().isoformat()
        }
    
    def _generate_mock_employee_performance(self) -> Dict[str, Any]:
        """بيانات تجريبية لأداء الموظفين"""
        return {
            "title": "تقرير أداء الموظفين",
            "data": [
                {"employee_name": "أحمد محمد", "employee_id": "EMP001", "department": "المختبر الرئيسي", "total_leave_requests": 8, "total_leave_days": 25, "coverage_requests": 3, "shift_swap_requests": 2, "avg_leave_duration": 3.1},
                {"employee_name": "فاطمة علي", "employee_id": "EMP002", "department": "المختبر الرئيسي", "total_leave_requests": 6, "total_leave_days": 18, "coverage_requests": 2, "shift_swap_requests": 4, "avg_leave_duration": 3.0}
            ],
            "generated_at": datetime.now().isoformat()
        }
    
    def _generate_mock_department_analytics(self) -> Dict[str, Any]:
        """بيانات تجريبية لتحليلات الأقسام"""
        return {
            "title": "تحليلات الأقسام",
            "data": [
                {"department_name": "المختبر الرئيسي", "total_employees": 45, "total_leave_requests": 156, "approved_leaves": 142, "avg_leave_duration": 4.2, "coverage_requests": 34, "shift_swaps": 28}
            ],
            "generated_at": datetime.now().isoformat()
        }
    
    def _calculate_performance_stats(self, data: List[Dict]) -> Dict[str, Any]:
        """حساب إحصائيات الأداء"""
        return {"total_employees": len(data)}
    
    def _prepare_performance_chart_data(self, data: List[Dict]) -> Dict[str, Any]:
        """تحضير بيانات رسم الأداء"""
        return {"labels": [], "data": []}
    
    def _calculate_department_stats(self, data: List[Dict]) -> Dict[str, Any]:
        """حساب إحصائيات الأقسام"""
        return {"total_departments": len(data)}
    
    def _prepare_department_chart_data(self, data: List[Dict]) -> Dict[str, Any]:
        """تحضير بيانات رسم الأقسام"""
        return {"labels": [], "data": []}


# إنشاء مثيل عام
reports_manager = EnhancedReportsManager()
