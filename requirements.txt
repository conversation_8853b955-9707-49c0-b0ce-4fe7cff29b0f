# ===== ALEMIS - نظام إدارة إجازات الموظفين =====
# Enhanced Laboratory Employee Leave Management System
# Requirements File - Production Ready

# ===== Core Flask Framework =====
flask==3.0.0
Werkzeug==3.0.1
Jinja2==3.1.2
itsdangerous==2.2.0
MarkupSafe==2.1.3
click==8.1.7

# ===== Database & ORM =====
SQLAlchemy==2.0.23
flask-sqlalchemy==3.1.1
flask-migrate==4.0.5
alembic==1.13.1

# ===== Authentication & Security =====
flask-login==0.6.3
flask-wtf==1.2.1
WTForms==3.1.1
bcrypt==4.1.2
cryptography==41.0.8
PyJWT==2.8.0
pyotp==2.9.0

# ===== Web Security =====
flask-talisman==1.1.0
flask-limiter==3.5.0
flask-cors==4.0.0

# ===== Performance & Caching =====
flask-caching==2.1.0
flask-compress==1.14
redis==5.0.1

# ===== Email & Notifications =====
flask-mail==0.9.1
celery==5.3.4

# ===== Internationalization =====
flask-babel==3.1.0
flask-moment==1.0.6

# ===== API & Serialization =====
flask-restx==1.3.0
marshmallow==3.20.2
marshmallow-sqlalchemy==0.29.0

# ===== File Handling & PDF =====
weasyprint==60.2
reportlab==4.0.7
openpyxl==3.1.2
Pillow==10.1.0

# ===== Utilities =====
python-dateutil==2.8.2
requests==2.31.0
python-dotenv==1.0.0

# ===== Development & Testing =====
pytest==7.4.3
pytest-cov==4.1.0
pytest-flask==1.3.0
black==23.11.0
flake8==6.1.0
isort==5.12.0

# ===== Production Server =====
gunicorn==21.2.0
gevent==23.9.1

# ===== Monitoring & Logging =====
sentry-sdk[flask]==1.38.0
structlog==23.2.0
prometheus-client==0.19.0

# ===== Optional Dependencies (commented for basic installation) =====
# Uncomment as needed for advanced features

# Advanced Analytics
# pandas==2.1.4
# numpy==1.25.2
# matplotlib==3.8.2
# seaborn==0.13.0

# Advanced Database Support
# psycopg2-binary==2.9.9  # PostgreSQL
# pymysql==1.1.0          # MySQL

# Message Queue
# kombu==5.3.4
# billiard==4.2.0

# Advanced Monitoring
# elastic-apm==6.20.0
# newrelic==9.2.0
