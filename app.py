#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الموظفين - Employee Management System
نظام شامل لإدارة الموظفين والإجازات والمناوبات
الإصدار الكامل مع جميع التحسينات والميزات المتقدمة
"""

import os
import sys
import sqlite3
import hashlib
import calendar
import json
import secrets
from datetime import datetime, timedelta, date
from flask import Flask, render_template, redirect, url_for, flash, request, jsonify, session, g

# استيراد الوحدات المحسنة
ENHANCED_MODULES_AVAILABLE = False
security_manager = None
cache_manager = None
reports_manager = None
db_manager = None

# تعطيل الوحدات المتقدمة مؤقتاً لضمان عمل النظام الأساسي
reports_manager = None
cache_manager = None
security_manager = None
db_manager = None
ENHANCED_MODULES_AVAILABLE = False

print("⚠️ تم تعطيل الوحدات المتقدمة مؤقتاً - النظام يعمل في الوضع الأساسي")

# إنشاء التطبيق مع التحسينات
app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', secrets.token_hex(32))
app.config['DATABASE'] = 'alemis.db'
app.config['DEBUG'] = True

# تحديد حالة الوحدات المحسنة
if reports_manager or cache_manager or security_manager or db_manager:
    ENHANCED_MODULES_AVAILABLE = True
    print("✅ تم تحميل بعض الوحدات المحسنة بنجاح")
else:
    ENHANCED_MODULES_AVAILABLE = False
    print("⚠️ تشغيل في الوضع الأساسي - الوحدات المحسنة غير متاحة")

# إعداد قاعدة البيانات المحسن
def get_db():
    """الحصول على اتصال قاعدة البيانات مع التحسينات"""
    db = getattr(g, '_database', None)
    if db is None:
        db = g._database = sqlite3.connect(app.config['DATABASE'])
        db.row_factory = sqlite3.Row
        # تحسينات الأداء
        db.execute('PRAGMA journal_mode=WAL')
        db.execute('PRAGMA synchronous=NORMAL')
        db.execute('PRAGMA cache_size=10000')
        db.execute('PRAGMA temp_store=MEMORY')
    return db

def query_db(query, args=(), one=False):
    cur = get_db().execute(query, args)
    rv = cur.fetchall()
    cur.close()

    # تحويل النتائج إلى قواميس
    if rv:
        if one:
            return dict(rv[0])
        else:
            return [dict(row) for row in rv]
    else:
        return None if one else []

@app.teardown_appcontext
def close_connection(exception):
    db = getattr(g, '_database', None)
    if db is not None:
        db.close()

# وظائف المساعدة
def hash_password(password):
    """تشفير كلمة المرور"""
    return hashlib.sha256(password.encode()).hexdigest()

def check_password(hashed_password, password):
    """التحقق من كلمة المرور"""
    return hashed_password == hash_password(password)

def is_authenticated():
    """التحقق من تسجيل الدخول"""
    return 'user_id' in session

def get_current_user():
    """الحصول على المستخدم الحالي"""
    if 'user_id' in session:
        return query_db('SELECT * FROM users WHERE id = ?', [session['user_id']], one=True)
    return None

def init_db():
    """تهيئة قاعدة البيانات"""
    with app.app_context():
        db = get_db()
        with open('schema.sql', 'r', encoding='utf-8') as f:
            db.executescript(f.read())
        db.commit()

        # إضافة البيانات الأساسية
        init_basic_data()

def init_basic_data():
    """إضافة البيانات الأساسية"""
    db = get_db()

    # إضافة الأقسام
    departments = [
        ('المختبر الكيميائي', 'قسم المختبر الكيميائي'),
        ('المختبر البيولوجي', 'قسم المختبر البيولوجي'),
        ('مختبر الأبحاث', 'قسم مختبر الأبحاث'),
        ('الإدارة', 'قسم الإدارة'),
        ('الموارد البشرية', 'قسم الموارد البشرية')
    ]

    for name, description in departments:
        existing = query_db('SELECT id FROM departments WHERE name = ?', [name], one=True)
        if not existing:
            db.execute('INSERT INTO departments (name, description) VALUES (?, ?)', [name, description])

    # إضافة أنواع الإجازات
    leave_types = [
        ('إجازة اعتيادية', 'الإجازة السنوية العادية', 30),
        ('إجازة مرضية', 'إجازة مرضية بتقرير طبي', 15),
        ('إجازة اضطرارية', 'إجازة اضطرارية لظروف طارئة', 7),
        ('إجازة بدون راتب', 'إجازة بدون راتب', 0),
        ('بدل يوم تغطية', 'بدل عن يوم تغطية في الإجازة', 0),
        ('إجازة أمومة', 'إجازة أمومة', 70),
        ('إجازة حج', 'إجازة حج', 15),
        ('إجازة دراسية', 'إجازة دراسية', 0)
    ]

    for name, description, default_days in leave_types:
        existing = query_db('SELECT id FROM leave_types WHERE name = ?', [name], one=True)
        if not existing:
            db.execute('INSERT INTO leave_types (name, description, default_days) VALUES (?, ?, ?)',
                      [name, description, default_days])

    # إضافة المستخدمين الأساسيين
    users = [
        ('admin', '<EMAIL>', hash_password('admin123'), 'مدير', 'النظام', 'admin', 4),
        ('hr', '<EMAIL>', hash_password('admin123'), 'مدير', 'الموارد البشرية', 'hr', 5),
        ('gm', '<EMAIL>', hash_password('admin123'), 'المدير', 'العام', 'gm', 4),
        ('lab_manager', '<EMAIL>', hash_password('admin123'), 'مدير', 'المختبر', 'manager', 1),
        ('employee1', '<EMAIL>', hash_password('emp123'), 'موظف', 'أول', 'employee', 1),
        ('employee2', '<EMAIL>', hash_password('emp123'), 'موظف', 'ثاني', 'employee', 2)
    ]

    for username, email, password_hash, first_name, last_name, role, dept_id in users:
        existing = query_db('SELECT id FROM users WHERE username = ?', [username], one=True)
        if not existing:
            db.execute('''INSERT INTO users
                         (username, email, password_hash, first_name, last_name, role, department_id, is_active)
                         VALUES (?, ?, ?, ?, ?, ?, ?, 1)''',
                      [username, email, password_hash, first_name, last_name, role, dept_id])

    # إضافة أرصدة الإجازات للمستخدمين
    users_list = query_db('SELECT id FROM users WHERE role = "employee"') or []
    leave_types_list = query_db('SELECT id, default_days FROM leave_types WHERE default_days > 0') or []
    current_year = datetime.now().year

    for user in users_list:
        for leave_type in leave_types_list:
            existing = query_db('''SELECT id FROM leave_balances
                                  WHERE user_id = ? AND leave_type_id = ? AND year = ?''',
                               [user['id'], leave_type['id'], current_year], one=True)
            if not existing:
                db.execute('''INSERT INTO leave_balances
                             (user_id, leave_type_id, year, total_days, used_days, remaining_days)
                             VALUES (?, ?, ?, ?, 0, ?)''',
                          [user['id'], leave_type['id'], current_year,
                           leave_type['default_days'], leave_type['default_days']])

    db.commit()
    print("✅ تم إضافة البيانات الأساسية بنجاح")

# المسارات الأساسية المحسنة
@app.route('/')
def index():
    """الصفحة الرئيسية المحسنة"""
    if is_authenticated():
        return redirect(url_for('dashboard'))
    return render_template('login.html')

@app.route('/health')
def health_check():
    """فحص صحة النظام"""
    try:
        # فحص قاعدة البيانات
        db = get_db()
        db.execute('SELECT 1').fetchone()

        # فحص الوحدات المحسنة
        modules_status = {
            'enhanced_modules': ENHANCED_MODULES_AVAILABLE,
            'database': True,
            'cache': cache_manager.is_healthy() if cache_manager else True,
            'security': True,
            'reports': reports_manager is not None,
            'db_manager': db_manager is not None
        }

        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'modules': modules_status,
            'version': '2.0.0-enhanced'
        })
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/stats')
def api_stats():
    """API للإحصائيات المحسنة"""
    if not is_authenticated():
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        if reports_manager:
            stats = reports_manager.get_dashboard_stats()
        else:
            # إحصائيات أساسية
            stats = {
                'total_employees': 0,
                'on_leave_today': 0,
                'pending_requests': 0,
                'monthly_requests': 0
            }

            result = query_db('SELECT COUNT(*) as count FROM users WHERE role = "employee"', one=True)
            if result:
                stats['total_employees'] = result['count'] if result else 0

        return jsonify(stats)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/login', methods=['GET', 'POST'])
def login():
    """تسجيل الدخول"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        user = query_db('SELECT * FROM users WHERE username = ?', [username], one=True)
        
        if user and check_password(user['password_hash'], password):
            session['user_id'] = user['id']
            session['username'] = user['username']
            session['role'] = user['role']
            
            flash('تم تسجيل الدخول بنجاح', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'danger')
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    """تسجيل الخروج"""
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
def dashboard():
    """لوحة التحكم المحسنة"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى لوحة التحكم', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    try:
        if reports_manager:
            # استخدام مدير التقارير المحسن
            stats = reports_manager.get_dashboard_stats()
            charts_data = reports_manager.get_dashboard_charts()
            recent_activities = reports_manager.get_recent_activities(limit=10)

            return render_template('dashboard.html',
                                 user=user,
                                 stats=stats,
                                 charts_data=charts_data,
                                 recent_activities=recent_activities,
                                 enhanced_mode=True)
        else:
            # الوضع الأساسي
            stats = {
                'total_employees': 0,
                'on_leave_today': 0,
                'pending_requests': 0,
                'monthly_requests': 0
            }

            # محاولة الحصول على الإحصائيات الأساسية
            result = query_db('SELECT COUNT(*) as count FROM users WHERE role = "employee" AND is_active = 1', one=True)
            if result:
                stats['total_employees'] = result['count']

            result = query_db('SELECT COUNT(*) as count FROM leave_requests WHERE status = "pending"', one=True)
            if result:
                stats['pending_requests'] = result['count']

            result = query_db('''
                SELECT COUNT(*) as count FROM leave_requests
                WHERE strftime('%Y-%m', created_at) = ?
            ''', [datetime.now().strftime('%Y-%m')], one=True)
            if result:
                stats['monthly_requests'] = result['count']

            return render_template('dashboard.html',
                                 user=user,
                                 stats=stats,
                                 enhanced_mode=False)

    except Exception as e:
        print(f"خطأ في لوحة التحكم: {e}")
        flash('حدث خطأ في تحميل لوحة التحكم', 'error')
        return render_template('dashboard.html',
                             user=user,
                             stats={'total_employees': 0, 'on_leave_today': 0, 'pending_requests': 0, 'monthly_requests': 0},
                             enhanced_mode=False)


@app.route('/executive_dashboard')
def executive_dashboard():
    """لوحة التحكم التنفيذية"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # التحقق من صلاحيات المستخدم
    if user and user['role'] not in ['admin', 'gm']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    try:
        return render_template('executive_dashboard.html', user=user)
    except Exception as e:
        print(f"خطأ في لوحة التحكم التنفيذية: {e}")
        flash('حدث خطأ في تحميل لوحة التحكم التنفيذية', 'error')
        return redirect(url_for('dashboard'))


@app.route('/departments_management')
def departments_management():
    """إدارة الأقسام"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # التحقق من صلاحيات المستخدم
    if user and user['role'] not in ['admin', 'hr', 'gm']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    try:
        return render_template('departments_management.html', user=user)
    except Exception as e:
        print(f"خطأ في إدارة الأقسام: {e}")
        flash('حدث خطأ في تحميل صفحة إدارة الأقسام', 'error')
        return redirect(url_for('dashboard'))


@app.route('/performance_evaluation')
def performance_evaluation():
    """نظام تقييم الأداء"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # التحقق من صلاحيات المستخدم
    if user and user.get('role') not in ['admin', 'hr', 'gm', 'manager']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    try:
        return render_template('performance_evaluation.html', user=user)
    except Exception as e:
        print(f"خطأ في نظام تقييم الأداء: {e}")
        flash('حدث خطأ في تحميل صفحة تقييم الأداء', 'error')
        return redirect(url_for('dashboard'))


@app.route('/project_management')
def project_management():
    """نظام إدارة المشاريع"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # التحقق من صلاحيات المستخدم
    if user and user.get('role') not in ['admin', 'gm', 'manager']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    try:
        return render_template('project_management.html', user=user)
    except Exception as e:
        print(f"خطأ في نظام إدارة المشاريع: {e}")
        flash('حدث خطأ في تحميل صفحة إدارة المشاريع', 'error')
        return redirect(url_for('dashboard'))


@app.route('/smart_attendance')
def smart_attendance():
    """نظام الحضور والانصراف الذكي"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    try:
        return render_template('smart_attendance.html', user=user)
    except Exception as e:
        print(f"خطأ في نظام الحضور الذكي: {e}")
        flash('حدث خطأ في تحميل صفحة الحضور الذكي', 'error')
        return redirect(url_for('dashboard'))


@app.route('/users')
def users():
    """قائمة المستخدمين"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # التحقق من صلاحيات المستخدم
    if user and user.get('role') not in ['admin', 'hr', 'gm']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    try:
        # قائمة وهمية للمستخدمين (يمكن استبدالها بقاعدة البيانات الحقيقية)
        users_list = [
            {'id': 1, 'username': 'admin', 'first_name': 'مدير', 'last_name': 'النظام', 'email': '<EMAIL>', 'role': 'admin', 'is_active': True},
            {'id': 2, 'username': 'hr', 'first_name': 'مدير', 'last_name': 'الموارد البشرية', 'email': '<EMAIL>', 'role': 'hr', 'is_active': True},
            {'id': 3, 'username': 'gm', 'first_name': 'المدير', 'last_name': 'العام', 'email': '<EMAIL>', 'role': 'gm', 'is_active': True},
            {'id': 4, 'username': 'lab_manager', 'first_name': 'مدير', 'last_name': 'المختبر', 'email': '<EMAIL>', 'role': 'manager', 'is_active': True},
            {'id': 5, 'username': 'rad_manager', 'first_name': 'مدير', 'last_name': 'الأشعة', 'email': '<EMAIL>', 'role': 'manager', 'is_active': True},
        ]

        return render_template('users.html', user=user, users=users_list)
    except Exception as e:
        print(f"خطأ في قائمة المستخدمين: {e}")
        flash('حدث خطأ في تحميل قائمة المستخدمين', 'error')
        return redirect(url_for('dashboard'))


@app.route('/new_user')
def new_user():
    """إضافة مستخدم جديد"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # التحقق من صلاحيات المستخدم
    if user and user.get('role') not in ['admin', 'hr']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    flash('ميزة إضافة مستخدم جديد قيد التطوير', 'info')
    return redirect(url_for('users'))


@app.route('/edit_user/<int:user_id>')
def edit_user(user_id):
    """تعديل مستخدم"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # التحقق من صلاحيات المستخدم
    if user and user.get('role') not in ['admin', 'hr']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    flash(f'ميزة تعديل المستخدم رقم {user_id} قيد التطوير', 'info')
    return redirect(url_for('users'))


@app.route('/delete_user/<int:user_id>', methods=['POST'])
def delete_user(user_id):
    """حذف مستخدم"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # التحقق من صلاحيات المستخدم
    if user and user.get('role') not in ['admin', 'hr']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    flash(f'ميزة حذف المستخدم رقم {user_id} قيد التطوير', 'info')
    return redirect(url_for('users'))



# معالجات الأخطاء
@app.errorhandler(404)
def not_found_error(error):
    try:
        return render_template('errors/404.html'), 404
    except:
        return jsonify({'error': 'الصفحة غير موجودة'}), 404

@app.errorhandler(500)
def internal_error(error):
    try:
        return render_template('errors/500.html'), 500
    except:
        return jsonify({'error': 'خطأ داخلي في الخادم'}), 500

@app.errorhandler(403)
def forbidden_error(error):
    try:
        return render_template('errors/403.html'), 403
    except:
        return jsonify({'error': 'غير مسموح بالوصول'}), 403

# مسارات التقارير المحسنة
@app.route('/reports')
def reports():
    """صفحة التقارير المحسنة"""
    if not is_authenticated():
        return redirect(url_for('login'))

    user = get_current_user()
    if user['role'] not in ['admin', 'hr', 'manager']:
        flash('غير مصرح لك بالوصول إلى التقارير', 'error')
        return redirect(url_for('dashboard'))

    try:
        if reports_manager:
            reports_data = reports_manager.get_all_reports()
            return render_template('reports.html',
                                 user=user,
                                 reports_data=reports_data,
                                 enhanced_mode=True)
        else:
            return render_template('reports.html',
                                 user=user,
                                 enhanced_mode=False)
    except Exception as e:
        print(f"خطأ في التقارير: {e}")
        flash('حدث خطأ في تحميل التقارير', 'error')
        return redirect(url_for('dashboard'))

@app.route('/interactive_reports')
def interactive_reports():
    """التقارير التفاعلية المحسنة"""
    if not is_authenticated():
        return redirect(url_for('login'))

    user = get_current_user()
    if user['role'] not in ['admin', 'hr', 'manager']:
        flash('غير مصرح لك بالوصول إلى التقارير التفاعلية', 'error')
        return redirect(url_for('dashboard'))

    try:
        if reports_manager:
            interactive_data = reports_manager.get_interactive_reports()
            return render_template('interactive_reports.html',
                                 user=user,
                                 interactive_data=interactive_data,
                                 enhanced_mode=True)
        else:
            return render_template('interactive_reports.html',
                                 user=user,
                                 enhanced_mode=False)
    except Exception as e:
        print(f"خطأ في التقارير التفاعلية: {e}")
        flash('حدث خطأ في تحميل التقارير التفاعلية', 'error')
        return redirect(url_for('dashboard'))

# المسارات المفقودة - إضافة المسارات الأساسية للنظام

@app.route('/leave_requests')
def leave_requests():
    """طلبات الإجازات"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()
    if user and user.get('role') not in ['admin', 'hr', 'manager', 'gm']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    try:
        # جلب طلبات الإجازات من قاعدة البيانات
        leave_requests = query_db('''
            SELECT lr.*, u.first_name, u.last_name, u.username, lt.name as leave_type_name,
                   d.name as department_name
            FROM leave_requests lr
            JOIN users u ON lr.user_id = u.id
            JOIN leave_types lt ON lr.leave_type_id = lt.id
            LEFT JOIN departments d ON u.department_id = d.id
            ORDER BY lr.created_at DESC
        ''')

        return render_template('leave_requests.html',
                             user=user,
                             leave_requests=leave_requests or [])
    except Exception as e:
        print(f"خطأ في طلبات الإجازات: {e}")
        flash('حدث خطأ في تحميل طلبات الإجازات', 'error')
        return redirect(url_for('dashboard'))

@app.route('/coverage_requests')
def coverage_requests():
    """طلبات التغطية"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()
    if user and user.get('role') not in ['admin', 'hr', 'manager', 'gm']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    try:
        # جلب طلبات التغطية من قاعدة البيانات
        coverage_requests = query_db('''
            SELECT cr.*, u.first_name, u.last_name, u.username,
                   cu.first_name as covering_first_name, cu.last_name as covering_last_name
            FROM coverage_requests cr
            JOIN leave_requests lr ON cr.leave_request_id = lr.id
            JOIN users u ON lr.user_id = u.id
            JOIN users cu ON cr.covering_user_id = cu.id
            ORDER BY cr.created_at DESC
        ''')

        return render_template('coverage_requests.html',
                             user=user,
                             coverage_requests=coverage_requests or [])
    except Exception as e:
        print(f"خطأ في طلبات التغطية: {e}")
        flash('حدث خطأ في تحميل طلبات التغطية', 'error')
        return redirect(url_for('dashboard'))

@app.route('/shift_swap_requests')
def shift_swap_requests():
    """طلبات تبديل الدوام"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    try:
        # جلب طلبات تبديل الدوام من قاعدة البيانات
        swap_requests = query_db('''
            SELECT ssr.*, u1.first_name as requester_first_name, u1.last_name as requester_last_name,
                   u2.first_name as target_first_name, u2.last_name as target_last_name
            FROM shift_swap_requests ssr
            JOIN users u1 ON ssr.requester_id = u1.id
            LEFT JOIN users u2 ON ssr.target_user_id = u2.id
            WHERE ssr.requester_id = ? OR ssr.target_user_id = ? OR ? IN ('admin', 'hr', 'manager', 'gm')
            ORDER BY ssr.created_at DESC
        ''', [user['id'], user['id'], user['role']])

        return render_template('shift_swap_requests.html',
                             user=user,
                             shift_swap_requests=swap_requests or [])
    except Exception as e:
        print(f"خطأ في طلبات تبديل الدوام: {e}")
        flash('حدث خطأ في تحميل طلبات تبديل الدوام', 'error')
        return redirect(url_for('dashboard'))

@app.route('/other_requests')
def other_requests():
    """الطلبات الأخرى"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()
    if user and user.get('role') not in ['admin', 'hr', 'manager', 'gm']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    try:
        # جلب الطلبات الأخرى من قاعدة البيانات
        other_requests = query_db('''
            SELECT otr.*, u.first_name, u.last_name, u.username
            FROM other_requests otr
            JOIN users u ON otr.user_id = u.id
            ORDER BY otr.created_at DESC
        ''')

        return render_template('other_requests.html',
                             user=user,
                             other_requests=other_requests or [])
    except Exception as e:
        print(f"خطأ في الطلبات الأخرى: {e}")
        flash('حدث خطأ في تحميل الطلبات الأخرى', 'error')
        return redirect(url_for('dashboard'))

@app.route('/new_leave')
def new_leave():
    """طلب إجازة جديدة"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    try:
        # جلب أنواع الإجازات
        leave_types = query_db('SELECT * FROM leave_types ORDER BY name')

        return render_template('new_leave.html',
                             user=user,
                             leave_types=leave_types or [])
    except Exception as e:
        print(f"خطأ في صفحة طلب إجازة جديدة: {e}")
        flash('حدث خطأ في تحميل صفحة طلب الإجازة', 'error')
        return redirect(url_for('dashboard'))

@app.route('/my_leaves')
def my_leaves():
    """إجازاتي"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    try:
        # جلب إجازات المستخدم
        my_leaves = query_db('''
            SELECT lr.*, lt.name as leave_type_name
            FROM leave_requests lr
            JOIN leave_types lt ON lr.leave_type_id = lt.id
            WHERE lr.user_id = ?
            ORDER BY lr.created_at DESC
        ''', [user['id']])

        return render_template('my_leaves.html',
                             user=user,
                             leave_requests=my_leaves or [])
    except Exception as e:
        print(f"خطأ في صفحة إجازاتي: {e}")
        flash('حدث خطأ في تحميل إجازاتك', 'error')
        return redirect(url_for('dashboard'))

@app.route('/my_shift_schedule')
def my_shift_schedule():
    """جدول دوامي"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    try:
        # جلب جدول الدوام للمستخدم
        shift_schedule = query_db('''
            SELECT * FROM shift_schedules
            WHERE user_id = ?
            ORDER BY schedule_date DESC
        ''', [user['id']])

        return render_template('my_shift_schedule.html',
                             user=user,
                             shift_schedule=shift_schedule or [])
    except Exception as e:
        print(f"خطأ في جدول الدوام: {e}")
        flash('حدث خطأ في تحميل جدول الدوام', 'error')
        return redirect(url_for('dashboard'))

@app.route('/direct_shift_swap')
def direct_shift_swap():
    """تبديل دوام مباشر"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    try:
        # جلب الموظفين المتاحين للتبديل
        available_employees = query_db('''
            SELECT id, first_name, last_name, username
            FROM users
            WHERE role = 'employee' AND is_active = 1 AND id != ?
            ORDER BY first_name, last_name
        ''', [user['id']])

        return render_template('direct_shift_swap.html',
                             user=user,
                             available_employees=available_employees or [])
    except Exception as e:
        print(f"خطأ في تبديل الدوام المباشر: {e}")
        flash('حدث خطأ في تحميل صفحة تبديل الدوام', 'error')
        return redirect(url_for('dashboard'))

@app.route('/new_shift_swap')
def new_shift_swap():
    """طلب تبديل دوام جديد"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    try:
        # جلب الموظفين المتاحين للتبديل
        available_employees = query_db('''
            SELECT id, first_name, last_name, username
            FROM users
            WHERE role = 'employee' AND is_active = 1 AND id != ?
            ORDER BY first_name, last_name
        ''', [user['id']])

        return render_template('new_shift_swap.html',
                             user=user,
                             available_employees=available_employees or [])
    except Exception as e:
        print(f"خطأ في طلب تبديل الدوام: {e}")
        flash('حدث خطأ في تحميل صفحة طلب تبديل الدوام', 'error')
        return redirect(url_for('dashboard'))

@app.route('/new_coverage')
def new_coverage():
    """طلب تغطية جديد"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    try:
        return render_template('new_coverage.html', user=user)
    except Exception as e:
        print(f"خطأ في طلب التغطية: {e}")
        flash('حدث خطأ في تحميل صفحة طلب التغطية', 'error')
        return redirect(url_for('dashboard'))

@app.route('/my_coverage')
def my_coverage():
    """تغطياتي"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    try:
        # جلب طلبات التغطية للمستخدم
        my_coverage = query_db('''
            SELECT cr.*, u.first_name, u.last_name
            FROM coverage_requests cr
            JOIN leave_requests lr ON cr.leave_request_id = lr.id
            JOIN users u ON lr.user_id = u.id
            WHERE cr.covering_user_id = ?
            ORDER BY cr.created_at DESC
        ''', [user['id']])

        return render_template('my_coverage.html',
                             user=user,
                             coverage_requests=my_coverage or [])
    except Exception as e:
        print(f"خطأ في تغطياتي: {e}")
        flash('حدث خطأ في تحميل تغطياتك', 'error')
        return redirect(url_for('dashboard'))

@app.route('/new_other_request')
def new_other_request():
    """طلب آخر جديد"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    try:
        return render_template('new_other_request.html', user=user)
    except Exception as e:
        print(f"خطأ في الطلب الآخر: {e}")
        flash('حدث خطأ في تحميل صفحة الطلب', 'error')
        return redirect(url_for('dashboard'))

@app.route('/permissions')
def permissions():
    """الأذونات"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    try:
        # جلب طلبات الأذونات للمستخدم
        permission_requests = query_db('''
            SELECT * FROM permission_requests
            WHERE user_id = ?
            ORDER BY created_at DESC
        ''', [user['id']])

        return render_template('permissions.html',
                             user=user,
                             permission_requests=permission_requests or [])
    except Exception as e:
        print(f"خطأ في الأذونات: {e}")
        flash('حدث خطأ في تحميل الأذونات', 'error')
        return redirect(url_for('dashboard'))

@app.route('/request_reports')
def request_reports():
    """تقارير الطلبات"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()
    if user and user['role'] not in ['admin', 'hr', 'manager', 'gm']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    try:
        return render_template('request_reports.html', user=user)
    except Exception as e:
        print(f"خطأ في تقارير الطلبات: {e}")
        flash('حدث خطأ في تحميل تقارير الطلبات', 'error')
        return redirect(url_for('dashboard'))

@app.route('/schedule')
def schedule():
    """جدول الإجازات"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()
    if user and user.get('role') not in ['admin', 'hr', 'manager', 'gm']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    try:
        return render_template('schedule.html', user=user)
    except Exception as e:
        print(f"خطأ في جدول الإجازات: {e}")
        flash('حدث خطأ في تحميل جدول الإجازات', 'error')
        return redirect(url_for('dashboard'))

@app.route('/shift_schedule')
def shift_schedule():
    """جدول الشفتات"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()
    if user and user.get('role') not in ['admin', 'hr', 'manager', 'gm']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    try:
        return render_template('shift_schedule.html', user=user)
    except Exception as e:
        print(f"خطأ في جدول الشفتات: {e}")
        flash('حدث خطأ في تحميل جدول الشفتات', 'error')
        return redirect(url_for('dashboard'))

@app.route('/leave_balances')
def leave_balances():
    """أرصدة الإجازات"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()
    if user and user.get('role') not in ['admin', 'hr', 'gm']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    try:
        # جلب أرصدة الإجازات
        leave_balances = query_db('''
            SELECT lb.*, u.first_name, u.last_name, u.username, lt.name as leave_type_name
            FROM leave_balances lb
            JOIN users u ON lb.user_id = u.id
            JOIN leave_types lt ON lb.leave_type_id = lt.id
            ORDER BY u.first_name, u.last_name, lt.name
        ''')

        return render_template('leave_balances.html',
                             user=user,
                             leave_balances=leave_balances or [])
    except Exception as e:
        print(f"خطأ في أرصدة الإجازات: {e}")
        flash('حدث خطأ في تحميل أرصدة الإجازات', 'error')
        return redirect(url_for('dashboard'))

@app.route('/monthly_operations')
def monthly_operations():
    """العمليات الشهرية"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()
    if user and user.get('role') not in ['gm']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    try:
        return render_template('monthly_operations.html', user=user)
    except Exception as e:
        print(f"خطأ في العمليات الشهرية: {e}")
        flash('حدث خطأ في تحميل العمليات الشهرية', 'error')
        return redirect(url_for('dashboard'))

@app.route('/interactive_dashboard')
def interactive_dashboard():
    """لوحة المعلومات التفاعلية"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()
    if user and user.get('role') not in ['admin', 'hr', 'manager', 'gm']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    try:
        return render_template('interactive_dashboard.html', user=user)
    except Exception as e:
        print(f"خطأ في لوحة المعلومات التفاعلية: {e}")
        flash('حدث خطأ في تحميل لوحة المعلومات التفاعلية', 'error')
        return redirect(url_for('dashboard'))

@app.route('/advanced_reports')
def advanced_reports():
    """التقارير المتقدمة"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()
    if user and user.get('role') not in ['admin', 'hr', 'manager', 'gm']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    try:
        return render_template('advanced_reports.html', user=user)
    except Exception as e:
        print(f"خطأ في التقارير المتقدمة: {e}")
        flash('حدث خطأ في تحميل التقارير المتقدمة', 'error')
        return redirect(url_for('dashboard'))

@app.route('/export_requests')
def export_requests():
    """تصدير الطلبات إلى PDF"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()
    if user and user.get('role') not in ['admin', 'hr', 'manager', 'gm']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    try:
        return render_template('export_requests.html', user=user)
    except Exception as e:
        print(f"خطأ في تصدير الطلبات: {e}")
        flash('حدث خطأ في تحميل صفحة تصدير الطلبات', 'error')
        return redirect(url_for('dashboard'))

@app.route('/profile_requests')
def profile_requests():
    """طلبات تعديل الملفات الشخصية"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()
    if user and user.get('role') not in ['manager']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    try:
        return render_template('profile_requests.html', user=user)
    except Exception as e:
        print(f"خطأ في طلبات تعديل الملفات: {e}")
        flash('حدث خطأ في تحميل طلبات تعديل الملفات', 'error')
        return redirect(url_for('dashboard'))

@app.route('/all_notifications')
def all_notifications():
    """جميع الإشعارات"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()
    if user and user.get('role') not in ['admin', 'hr', 'manager', 'gm']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    try:
        return render_template('all_notifications.html', user=user)
    except Exception as e:
        print(f"خطأ في الإشعارات: {e}")
        flash('حدث خطأ في تحميل الإشعارات', 'error')
        return redirect(url_for('dashboard'))

@app.route('/help_support')
def help_support():
    """المساعدة والدعم"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    try:
        return render_template('help_support.html', user=user)
    except Exception as e:
        print(f"خطأ في المساعدة والدعم: {e}")
        flash('حدث خطأ في تحميل صفحة المساعدة', 'error')
        return redirect(url_for('dashboard'))

@app.route('/profile')
def profile():
    """الملف الشخصي"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    try:
        return render_template('profile.html', user=user)
    except Exception as e:
        print(f"خطأ في الملف الشخصي: {e}")
        flash('حدث خطأ في تحميل الملف الشخصي', 'error')
        return redirect(url_for('dashboard'))

@app.route('/preferences')
def preferences():
    """الإعدادات"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    try:
        return render_template('preferences.html', user=user)
    except Exception as e:
        print(f"خطأ في الإعدادات: {e}")
        flash('حدث خطأ في تحميل الإعدادات', 'error')
        return redirect(url_for('dashboard'))

@app.route('/all_requests')
def all_requests():
    """جميع الطلبات"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    try:
        # جلب جميع الطلبات للمستخدم أو للإدارة
        if user and user.get('role') in ['admin', 'hr', 'manager', 'gm']:
            # للإدارة - جميع الطلبات
            all_requests = query_db('''
                SELECT 'leave' as request_type, id, user_id, status, created_at
                FROM leave_requests
                UNION ALL
                SELECT 'coverage' as request_type, id, covering_user_id as user_id, status, created_at
                FROM coverage_requests
                UNION ALL
                SELECT 'shift_swap' as request_type, id, requester_id as user_id, status, created_at
                FROM shift_swap_requests
                ORDER BY created_at DESC
            ''')
        else:
            # للموظفين - طلباتهم فقط
            all_requests = query_db('''
                SELECT 'leave' as request_type, id, user_id, status, created_at
                FROM leave_requests WHERE user_id = ?
                UNION ALL
                SELECT 'coverage' as request_type, id, covering_user_id as user_id, status, created_at
                FROM coverage_requests WHERE covering_user_id = ?
                UNION ALL
                SELECT 'shift_swap' as request_type, id, requester_id as user_id, status, created_at
                FROM shift_swap_requests WHERE requester_id = ?
                ORDER BY created_at DESC
            ''', [user.get('id'), user.get('id'), user.get('id')])

        return render_template('all_requests.html',
                             user=user,
                             all_requests=all_requests or [])
    except Exception as e:
        print(f"خطأ في جميع الطلبات: {e}")
        flash('حدث خطأ في تحميل الطلبات', 'error')
        return redirect(url_for('dashboard'))

# تشغيل التطبيق
if __name__ == '__main__':
    # التحقق من وجود قاعدة البيانات
    if not os.path.exists(app.config['DATABASE']):
        print("⚠️ قاعدة البيانات غير موجودة. جاري إنشاؤها...")
        try:
            init_db()
            print("✅ تم إنشاء قاعدة البيانات بنجاح")
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
    
    print("=" * 70)
    print("🏥 ALEMIS - نظام إدارة إجازات الموظفين المحسن")
    print("=" * 70)
    print("🌐 الخادم: http://localhost:5000")
    print("👤 بيانات تسجيل الدخول:")
    print("   - المدير: admin / admin123")
    print("   - الموارد البشرية: hr / admin123")
    print("   - المدير العام: gm / admin123")
    print("=" * 70)
    print(f"🔧 الوحدات المحسنة: {'✅ مفعلة' if ENHANCED_MODULES_AVAILABLE else '⚠️ معطلة'}")
    print("🎯 الميزات المتاحة:")
    print("   - لوحة تحكم تفاعلية")
    print("   - تقارير متقدمة ومرئية")
    print("   - نظام أمان محسن")
    print("   - تخزين مؤقت ذكي")
    print("   - واجهة مستخدم حديثة")
    print("   - تصدير PDF للطلبات")
    print("   - إشعارات ذكية")
    print("=" * 70)
    
    try:
        print("🚀 بدء تشغيل خادم Flask المحسن...")
        print("🌐 الخادم متاح على: http://localhost:5000")
        print("🔍 فحص الصحة: http://localhost:5000/health")
        print("📊 API الإحصائيات: http://localhost:5000/api/stats")
        print("📈 التقارير المحسنة: http://localhost:5000/reports")
        print("📊 التقارير التفاعلية: http://localhost:5000/interactive_reports")
        print("⏹️ اضغط Ctrl+C لإيقاف الخادم")
        print("-" * 70)
        app.run(debug=True, host='0.0.0.0', port=5000)
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        import traceback
        traceback.print_exc()
