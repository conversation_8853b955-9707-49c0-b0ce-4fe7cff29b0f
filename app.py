#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الموظفين - Employee Management System
نظام شامل لإدارة الموظفين والإجازات والمناوبات
الإصدار الكامل مع جميع التحسينات والميزات المتقدمة
"""

import os
import sys
import sqlite3
import hashlib
import calendar
import json
import secrets
from datetime import datetime, timedelta, date
from flask import Flask, render_template, redirect, url_for, flash, request, jsonify, session, g

# استيراد الوحدات المحسنة
ENHANCED_MODULES_AVAILABLE = False
security_manager = None
cache_manager = None
reports_manager = None
db_manager = None

# تعطيل الوحدات المتقدمة مؤقتاً لضمان عمل النظام الأساسي
reports_manager = None
cache_manager = None
security_manager = None
db_manager = None
ENHANCED_MODULES_AVAILABLE = False

print("⚠️ تم تعطيل الوحدات المتقدمة مؤقتاً - النظام يعمل في الوضع الأساسي")

# إنشاء التطبيق مع التحسينات
app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', secrets.token_hex(32))
app.config['DATABASE'] = 'alemis.db'
app.config['DEBUG'] = True

# تحديد حالة الوحدات المحسنة
if reports_manager or cache_manager or security_manager or db_manager:
    ENHANCED_MODULES_AVAILABLE = True
    print("✅ تم تحميل بعض الوحدات المحسنة بنجاح")
else:
    ENHANCED_MODULES_AVAILABLE = False
    print("⚠️ تشغيل في الوضع الأساسي - الوحدات المحسنة غير متاحة")

# إعداد قاعدة البيانات المحسن
def get_db():
    """الحصول على اتصال قاعدة البيانات مع التحسينات"""
    db = getattr(g, '_database', None)
    if db is None:
        db = g._database = sqlite3.connect(app.config['DATABASE'])
        db.row_factory = sqlite3.Row
        # تحسينات الأداء
        db.execute('PRAGMA journal_mode=WAL')
        db.execute('PRAGMA synchronous=NORMAL')
        db.execute('PRAGMA cache_size=10000')
        db.execute('PRAGMA temp_store=MEMORY')
    return db

def query_db(query, args=(), one=False):
    cur = get_db().execute(query, args)
    rv = cur.fetchall()
    cur.close()
    
    # تحويل النتائج إلى قواميس
    if rv:
        if one:
            return dict(rv[0]) if rv else None
        else:
            return [dict(row) for row in rv]
    else:
        return None if one else []

@app.teardown_appcontext
def close_connection(exception):
    db = getattr(g, '_database', None)
    if db is not None:
        db.close()

# وظائف المساعدة
def hash_password(password):
    """تشفير كلمة المرور"""
    return hashlib.sha256(password.encode()).hexdigest()

def check_password(hashed_password, password):
    """التحقق من كلمة المرور"""
    return hashed_password == hash_password(password)

def is_authenticated():
    """التحقق من تسجيل الدخول"""
    return 'user_id' in session

def get_current_user():
    """الحصول على المستخدم الحالي"""
    if 'user_id' in session:
        return query_db('SELECT * FROM users WHERE id = ?', [session['user_id']], one=True)
    return None

def init_db():
    """تهيئة قاعدة البيانات"""
    with app.app_context():
        db = get_db()
        with open('schema.sql', 'r', encoding='utf-8') as f:
            db.executescript(f.read())
        db.commit()

# المسارات الأساسية المحسنة
@app.route('/')
def index():
    """الصفحة الرئيسية المحسنة"""
    if is_authenticated():
        return redirect(url_for('dashboard'))
    return render_template('login.html')

@app.route('/health')
def health_check():
    """فحص صحة النظام"""
    try:
        # فحص قاعدة البيانات
        db = get_db()
        db.execute('SELECT 1').fetchone()

        # فحص الوحدات المحسنة
        modules_status = {
            'enhanced_modules': ENHANCED_MODULES_AVAILABLE,
            'database': True,
            'cache': cache_manager.is_healthy() if cache_manager else True,
            'security': True,
            'reports': reports_manager is not None,
            'db_manager': db_manager is not None
        }

        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'modules': modules_status,
            'version': '2.0.0-enhanced'
        })
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/stats')
def api_stats():
    """API للإحصائيات المحسنة"""
    if not is_authenticated():
        return jsonify({'error': 'غير مصرح'}), 401

    try:
        if reports_manager:
            stats = reports_manager.get_dashboard_stats()
        else:
            # إحصائيات أساسية
            stats = {
                'total_employees': 0,
                'on_leave_today': 0,
                'pending_requests': 0,
                'monthly_requests': 0
            }

            result = query_db('SELECT COUNT(*) as count FROM users WHERE role = "employee"', one=True)
            if result:
                stats['total_employees'] = result.get('count', 0)

        return jsonify(stats)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/login', methods=['GET', 'POST'])
def login():
    """تسجيل الدخول"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        user = query_db('SELECT * FROM users WHERE username = ?', [username], one=True)
        
        if user and check_password(user['password_hash'], password):
            session['user_id'] = user['id']
            session['username'] = user['username']
            session['role'] = user['role']
            
            flash('تم تسجيل الدخول بنجاح', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'danger')
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    """تسجيل الخروج"""
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
def dashboard():
    """لوحة التحكم المحسنة"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى لوحة التحكم', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    try:
        if reports_manager:
            # استخدام مدير التقارير المحسن
            stats = reports_manager.get_dashboard_stats()
            charts_data = reports_manager.get_dashboard_charts()
            recent_activities = reports_manager.get_recent_activities(limit=10)

            return render_template('dashboard.html',
                                 user=user,
                                 stats=stats,
                                 charts_data=charts_data,
                                 recent_activities=recent_activities,
                                 enhanced_mode=True)
        else:
            # الوضع الأساسي
            stats = {
                'total_employees': 0,
                'on_leave_today': 0,
                'pending_requests': 0,
                'monthly_requests': 0
            }

            # محاولة الحصول على الإحصائيات الأساسية
            result = query_db('SELECT COUNT(*) as count FROM users WHERE role = "employee" AND is_active = 1', one=True)
            if result:
                stats['total_employees'] = result.get('count', 0)

            result = query_db('SELECT COUNT(*) as count FROM leave_requests WHERE status = "pending"', one=True)
            if result:
                stats['pending_requests'] = result.get('count', 0)

            result = query_db('''
                SELECT COUNT(*) as count FROM leave_requests
                WHERE strftime('%Y-%m', created_at) = ?
            ''', [datetime.now().strftime('%Y-%m')], one=True)
            if result:
                stats['monthly_requests'] = result.get('count', 0)

            return render_template('dashboard.html',
                                 user=user,
                                 stats=stats,
                                 enhanced_mode=False)

    except Exception as e:
        print(f"خطأ في لوحة التحكم: {e}")
        flash('حدث خطأ في تحميل لوحة التحكم', 'error')
        return render_template('dashboard.html',
                             user=user,
                             stats={'total_employees': 0, 'on_leave_today': 0, 'pending_requests': 0, 'monthly_requests': 0},
                             enhanced_mode=False)


@app.route('/executive_dashboard')
def executive_dashboard():
    """لوحة التحكم التنفيذية"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # التحقق من صلاحيات المستخدم
    if user and user.get('role') not in ['admin', 'gm']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    try:
        return render_template('executive_dashboard.html', user=user)
    except Exception as e:
        print(f"خطأ في لوحة التحكم التنفيذية: {e}")
        flash('حدث خطأ في تحميل لوحة التحكم التنفيذية', 'error')
        return redirect(url_for('dashboard'))


@app.route('/departments_management')
def departments_management():
    """إدارة الأقسام"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # التحقق من صلاحيات المستخدم
    if user and user.get('role') not in ['admin', 'hr', 'gm']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    try:
        return render_template('departments_management.html', user=user)
    except Exception as e:
        print(f"خطأ في إدارة الأقسام: {e}")
        flash('حدث خطأ في تحميل صفحة إدارة الأقسام', 'error')
        return redirect(url_for('dashboard'))


@app.route('/performance_evaluation')
def performance_evaluation():
    """نظام تقييم الأداء"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # التحقق من صلاحيات المستخدم
    if user and user.get('role') not in ['admin', 'hr', 'gm', 'manager']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    try:
        return render_template('performance_evaluation.html', user=user)
    except Exception as e:
        print(f"خطأ في نظام تقييم الأداء: {e}")
        flash('حدث خطأ في تحميل صفحة تقييم الأداء', 'error')
        return redirect(url_for('dashboard'))


@app.route('/project_management')
def project_management():
    """نظام إدارة المشاريع"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # التحقق من صلاحيات المستخدم
    if user and user.get('role') not in ['admin', 'gm', 'manager']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    try:
        return render_template('project_management.html', user=user)
    except Exception as e:
        print(f"خطأ في نظام إدارة المشاريع: {e}")
        flash('حدث خطأ في تحميل صفحة إدارة المشاريع', 'error')
        return redirect(url_for('dashboard'))


@app.route('/smart_attendance')
def smart_attendance():
    """نظام الحضور والانصراف الذكي"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    try:
        return render_template('smart_attendance.html', user=user)
    except Exception as e:
        print(f"خطأ في نظام الحضور الذكي: {e}")
        flash('حدث خطأ في تحميل صفحة الحضور الذكي', 'error')
        return redirect(url_for('dashboard'))


@app.route('/users')
def users():
    """قائمة المستخدمين"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # التحقق من صلاحيات المستخدم
    if user and user.get('role') not in ['admin', 'hr', 'gm']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    try:
        # قائمة وهمية للمستخدمين (يمكن استبدالها بقاعدة البيانات الحقيقية)
        users_list = [
            {'id': 1, 'username': 'admin', 'first_name': 'مدير', 'last_name': 'النظام', 'email': '<EMAIL>', 'role': 'admin', 'is_active': True},
            {'id': 2, 'username': 'hr', 'first_name': 'مدير', 'last_name': 'الموارد البشرية', 'email': '<EMAIL>', 'role': 'hr', 'is_active': True},
            {'id': 3, 'username': 'gm', 'first_name': 'المدير', 'last_name': 'العام', 'email': '<EMAIL>', 'role': 'gm', 'is_active': True},
            {'id': 4, 'username': 'lab_manager', 'first_name': 'مدير', 'last_name': 'المختبر', 'email': '<EMAIL>', 'role': 'manager', 'is_active': True},
            {'id': 5, 'username': 'rad_manager', 'first_name': 'مدير', 'last_name': 'الأشعة', 'email': '<EMAIL>', 'role': 'manager', 'is_active': True},
        ]

        return render_template('users.html', user=user, users=users_list)
    except Exception as e:
        print(f"خطأ في قائمة المستخدمين: {e}")
        flash('حدث خطأ في تحميل قائمة المستخدمين', 'error')
        return redirect(url_for('dashboard'))


@app.route('/new_user')
def new_user():
    """إضافة مستخدم جديد"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # التحقق من صلاحيات المستخدم
    if user and user.get('role') not in ['admin', 'hr']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    flash('ميزة إضافة مستخدم جديد قيد التطوير', 'info')
    return redirect(url_for('users'))


@app.route('/edit_user/<int:user_id>')
def edit_user(user_id):
    """تعديل مستخدم"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # التحقق من صلاحيات المستخدم
    if user and user.get('role') not in ['admin', 'hr']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    flash(f'ميزة تعديل المستخدم رقم {user_id} قيد التطوير', 'info')
    return redirect(url_for('users'))


@app.route('/delete_user/<int:user_id>', methods=['POST'])
def delete_user(user_id):
    """حذف مستخدم"""
    if not is_authenticated():
        flash('يجب تسجيل الدخول للوصول إلى هذه الصفحة', 'warning')
        return redirect(url_for('login'))

    user = get_current_user()

    # التحقق من صلاحيات المستخدم
    if user and user.get('role') not in ['admin', 'hr']:
        flash('ليس لديك صلاحية للوصول إلى هذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    flash(f'ميزة حذف المستخدم رقم {user_id} قيد التطوير', 'info')
    return redirect(url_for('users'))



# معالجات الأخطاء
@app.errorhandler(404)
def not_found_error(error):
    try:
        return render_template('errors/404.html'), 404
    except:
        return jsonify({'error': 'الصفحة غير موجودة'}), 404

@app.errorhandler(500)
def internal_error(error):
    try:
        return render_template('errors/500.html'), 500
    except:
        return jsonify({'error': 'خطأ داخلي في الخادم'}), 500

@app.errorhandler(403)
def forbidden_error(error):
    try:
        return render_template('errors/403.html'), 403
    except:
        return jsonify({'error': 'غير مسموح بالوصول'}), 403

# مسارات التقارير المحسنة
@app.route('/reports')
def reports():
    """صفحة التقارير المحسنة"""
    if not is_authenticated():
        return redirect(url_for('login'))

    user = get_current_user()
    if user['role'] not in ['admin', 'hr', 'manager']:
        flash('غير مصرح لك بالوصول إلى التقارير', 'error')
        return redirect(url_for('dashboard'))

    try:
        if reports_manager:
            reports_data = reports_manager.get_all_reports()
            return render_template('reports.html',
                                 user=user,
                                 reports_data=reports_data,
                                 enhanced_mode=True)
        else:
            return render_template('reports.html',
                                 user=user,
                                 enhanced_mode=False)
    except Exception as e:
        print(f"خطأ في التقارير: {e}")
        flash('حدث خطأ في تحميل التقارير', 'error')
        return redirect(url_for('dashboard'))

@app.route('/interactive_reports')
def interactive_reports():
    """التقارير التفاعلية المحسنة"""
    if not is_authenticated():
        return redirect(url_for('login'))

    user = get_current_user()
    if user['role'] not in ['admin', 'hr', 'manager']:
        flash('غير مصرح لك بالوصول إلى التقارير التفاعلية', 'error')
        return redirect(url_for('dashboard'))

    try:
        if reports_manager:
            interactive_data = reports_manager.get_interactive_reports()
            return render_template('interactive_reports.html',
                                 user=user,
                                 interactive_data=interactive_data,
                                 enhanced_mode=True)
        else:
            return render_template('interactive_reports.html',
                                 user=user,
                                 enhanced_mode=False)
    except Exception as e:
        print(f"خطأ في التقارير التفاعلية: {e}")
        flash('حدث خطأ في تحميل التقارير التفاعلية', 'error')
        return redirect(url_for('dashboard'))

# تشغيل التطبيق
if __name__ == '__main__':
    # التحقق من وجود قاعدة البيانات
    if not os.path.exists(app.config['DATABASE']):
        print("⚠️ قاعدة البيانات غير موجودة. جاري إنشاؤها...")
        try:
            init_db()
            print("✅ تم إنشاء قاعدة البيانات بنجاح")
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
    
    print("=" * 70)
    print("🏥 ALEMIS - نظام إدارة إجازات الموظفين المحسن")
    print("=" * 70)
    print("🌐 الخادم: http://localhost:5000")
    print("👤 بيانات تسجيل الدخول:")
    print("   - المدير: admin / admin123")
    print("   - الموارد البشرية: hr / admin123")
    print("   - المدير العام: gm / admin123")
    print("=" * 70)
    print(f"🔧 الوحدات المحسنة: {'✅ مفعلة' if ENHANCED_MODULES_AVAILABLE else '⚠️ معطلة'}")
    print("🎯 الميزات المتاحة:")
    print("   - لوحة تحكم تفاعلية")
    print("   - تقارير متقدمة ومرئية")
    print("   - نظام أمان محسن")
    print("   - تخزين مؤقت ذكي")
    print("   - واجهة مستخدم حديثة")
    print("   - تصدير PDF للطلبات")
    print("   - إشعارات ذكية")
    print("=" * 70)
    
    try:
        print("🚀 بدء تشغيل خادم Flask المحسن...")
        print("🌐 الخادم متاح على: http://localhost:5000")
        print("🔍 فحص الصحة: http://localhost:5000/health")
        print("📊 API الإحصائيات: http://localhost:5000/api/stats")
        print("📈 التقارير المحسنة: http://localhost:5000/reports")
        print("📊 التقارير التفاعلية: http://localhost:5000/interactive_reports")
        print("⏹️ اضغط Ctrl+C لإيقاف الخادم")
        print("-" * 70)
        app.run(debug=True, host='0.0.0.0', port=5000)
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        import traceback
        traceback.print_exc()
