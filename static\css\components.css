/* ===== مكونات واجهة المستخدم الاحترافية ===== */
/* Professional UI Components */

/* ===== شريط التنقل الاحترافي ===== */
.navbar-professional {
    background: var(--white);
    border-bottom: 3px solid var(--primary-blue);
    box-shadow: var(--shadow-sm);
    padding: var(--space-md) 0;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar-brand-professional {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-blue);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.navbar-brand-professional:hover {
    color: var(--primary-blue-dark);
    text-decoration: none;
}

.navbar-nav-professional {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item-professional {
    position: relative;
}

.nav-link-professional {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-sm) var(--space-md);
    color: var(--charcoal);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.nav-link-professional:hover {
    background-color: var(--light-gray);
    color: var(--primary-blue);
    text-decoration: none;
}

.nav-link-professional.active {
    background-color: var(--primary-blue);
    color: var(--white);
}

/* ===== القوائم المنسدلة الاحترافية ===== */
.dropdown-professional {
    position: relative;
}

.dropdown-menu-professional {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--white);
    border: var(--border-light);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    min-width: 200px;
    padding: var(--space-sm) 0;
    z-index: 1050;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition-fast);
}

.dropdown-professional:hover .dropdown-menu-professional,
.dropdown-professional.show .dropdown-menu-professional {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item-professional {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-sm) var(--space-lg);
    color: var(--charcoal);
    text-decoration: none;
    transition: var(--transition-fast);
}

.dropdown-item-professional:hover {
    background-color: var(--light-gray);
    color: var(--primary-blue);
    text-decoration: none;
}

.dropdown-divider-professional {
    height: 1px;
    background-color: var(--medium-gray);
    margin: var(--space-sm) 0;
}

/* ===== بطاقات الإحصائيات ===== */
.stats-card {
    background: var(--white);
    border: var(--border-light);
    border-radius: var(--radius-xl);
    padding: var(--space-xl);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: var(--primary-blue);
}

.stats-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-4px);
}

.stats-card.success::before {
    background: var(--success);
}

.stats-card.warning::before {
    background: var(--warning);
}

.stats-card.danger::before {
    background: var(--danger);
}

.stats-card.info::before {
    background: var(--info);
}

.stats-number {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-blue);
    margin-bottom: var(--space-sm);
}

.stats-label {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    color: var(--charcoal);
    margin-bottom: var(--space-sm);
}

.stats-change {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.stats-change.positive {
    color: var(--success);
}

.stats-change.negative {
    color: var(--danger);
}

/* ===== الشارات والعلامات ===== */
.badge-professional {
    display: inline-flex;
    align-items: center;
    padding: var(--space-xs) var(--space-sm);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    line-height: 1;
    border-radius: var(--radius-full);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-primary {
    background-color: var(--primary-blue);
    color: var(--white);
}

.badge-success {
    background-color: var(--success);
    color: var(--white);
}

.badge-warning {
    background-color: var(--warning);
    color: var(--white);
}

.badge-danger {
    background-color: var(--danger);
    color: var(--white);
}

.badge-secondary {
    background-color: var(--dark-gray);
    color: var(--white);
}

.badge-light {
    background-color: var(--light-gray);
    color: var(--charcoal);
}

/* ===== التنبيهات الاحترافية ===== */
.alert-professional {
    padding: var(--space-lg);
    border: 1px solid transparent;
    border-radius: var(--radius-lg);
    margin-bottom: var(--space-lg);
    position: relative;
    display: flex;
    align-items: flex-start;
    gap: var(--space-md);
}

.alert-success {
    background-color: var(--success-light);
    border-color: var(--success);
    color: var(--success);
}

.alert-warning {
    background-color: var(--warning-light);
    border-color: var(--warning);
    color: var(--warning);
}

.alert-danger {
    background-color: var(--danger-light);
    border-color: var(--danger);
    color: var(--danger);
}

.alert-info {
    background-color: var(--info-light);
    border-color: var(--info);
    color: var(--info);
}

.alert-icon {
    font-size: var(--font-size-lg);
    margin-top: 2px;
}

.alert-content {
    flex: 1;
}

.alert-title {
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-xs);
}

.alert-message {
    margin: 0;
    opacity: 0.9;
}

/* ===== شريط التقدم الاحترافي ===== */
.progress-professional {
    height: 8px;
    background-color: var(--light-gray);
    border-radius: var(--radius-full);
    overflow: hidden;
    margin-bottom: var(--space-md);
}

.progress-bar-professional {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-blue), var(--primary-blue-light));
    border-radius: var(--radius-full);
    transition: width var(--transition-slow);
    position: relative;
}

.progress-bar-professional::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, 
        rgba(255,255,255,0.2) 25%, 
        transparent 25%, 
        transparent 50%, 
        rgba(255,255,255,0.2) 50%, 
        rgba(255,255,255,0.2) 75%, 
        transparent 75%);
    background-size: 20px 20px;
    animation: progress-stripes 1s linear infinite;
}

@keyframes progress-stripes {
    0% { background-position: 0 0; }
    100% { background-position: 20px 0; }
}

/* ===== الأيقونات الاحترافية ===== */
.icon-professional {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-lg);
    transition: var(--transition-fast);
}

.icon-primary {
    background-color: rgba(0, 102, 204, 0.1);
    color: var(--primary-blue);
}

.icon-success {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success);
}

.icon-warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning);
}

.icon-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger);
}

/* ===== الفواصل الاحترافية ===== */
.divider-professional {
    height: 1px;
    background: linear-gradient(90deg, 
        transparent 0%, 
        var(--medium-gray) 20%, 
        var(--medium-gray) 80%, 
        transparent 100%);
    margin: var(--space-xl) 0;
}

.divider-with-text {
    position: relative;
    text-align: center;
    margin: var(--space-xl) 0;
}

.divider-with-text::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--medium-gray);
}

.divider-text {
    background: var(--off-white);
    padding: 0 var(--space-lg);
    color: var(--dark-gray);
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-sm);
    position: relative;
    z-index: 1;
}

/* ===== الحاويات المرنة ===== */
.container-professional {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--space-lg);
}

.section-professional {
    padding: var(--space-3xl) 0;
}

.section-header {
    text-align: center;
    margin-bottom: var(--space-2xl);
}

.section-title {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--near-black);
    margin-bottom: var(--space-md);
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--dark-gray);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== الشبكة المرنة ===== */
.grid-professional {
    display: grid;
    gap: var(--space-lg);
}

.grid-2 { grid-template-columns: repeat(2, 1fr); }
.grid-3 { grid-template-columns: repeat(3, 1fr); }
.grid-4 { grid-template-columns: repeat(4, 1fr); }

@media (max-width: 768px) {
    .grid-2,
    .grid-3,
    .grid-4 {
        grid-template-columns: 1fr;
    }
}
