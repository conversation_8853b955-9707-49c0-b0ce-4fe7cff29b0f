@echo off
chcp 65001 >nul
title نظام إدارة الموظفين - Employee Management System

echo.
echo ==========================================
echo    نظام إدارة الموظفين
echo    Employee Management System
echo    الإصدار الاحترافي - Professional Edition
echo    🎨 السمة الفاتحة - Light Theme
echo ==========================================
echo.

echo 🔍 التحقق من Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo 💡 يرجى تثبيت Python 3.7 أو أحدث
    pause
    exit /b 1
)

echo ✅ Python متاح

echo.
echo 🔍 التحقق من Flask...
python -c "import flask" >nul 2>&1
if errorlevel 1 (
    echo ❌ Flask غير مثبت
    echo 📦 تثبيت Flask...
    pip install flask==3.0.0 Werkzeug==3.0.1
    if errorlevel 1 (
        echo ❌ فشل في تثبيت Flask
        pause
        exit /b 1
    )
)

echo ✅ Flask متاح

echo.
echo 🚀 بدء تشغيل النظام...
echo 🌐 سيتم فتح النظام على: http://127.0.0.1:5000
echo.
echo 👤 حسابات تجريبية:
echo    المدير: admin / admin123
echo    الموارد البشرية: hr / admin123
echo    المدير العام: gm / admin123
echo    موظف: employee1 / admin123
echo.
echo ⏳ انتظر قليلاً حتى يبدأ الخادم...
echo.

python run_simple.py

echo.
echo 🛑 تم إيقاف الخادم
pause
