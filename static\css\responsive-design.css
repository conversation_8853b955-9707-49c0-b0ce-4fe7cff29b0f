/* ===== التصميم المتجاوب والأداء المحسن ===== */
/* Responsive Design & Performance Optimization */

/* ===== متغيرات نقاط الكسر ===== */
:root {
    --breakpoint-xs: 480px;
    --breakpoint-sm: 576px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 992px;
    --breakpoint-xl: 1200px;
    --breakpoint-xxl: 1400px;
}

/* ===== تحسينات الأداء الأساسية ===== */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* تحسين الخطوط */
@font-display: swap;

/* ===== الشبكة المرنة ===== */
.container-fluid-responsive {
    width: 100%;
    padding-right: var(--space-lg);
    padding-left: var(--space-lg);
    margin-right: auto;
    margin-left: auto;
}

.container-responsive {
    width: 100%;
    padding-right: var(--space-lg);
    padding-left: var(--space-lg);
    margin-right: auto;
    margin-left: auto;
    max-width: var(--container-max-width);
}

.row-responsive {
    display: flex;
    flex-wrap: wrap;
    margin-right: calc(var(--space-md) * -1);
    margin-left: calc(var(--space-md) * -1);
}

.col-responsive {
    flex: 1 0 0%;
    padding-right: var(--space-md);
    padding-left: var(--space-md);
}

/* أعمدة مرنة */
.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
.col-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

/* ===== الشاشات الكبيرة جداً (XXL) ===== */
@media (min-width: 1400px) {
    .container-responsive {
        max-width: 1320px;
    }
    
    .col-xxl-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-xxl-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-xxl-3 { flex: 0 0 25%; max-width: 25%; }
    .col-xxl-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-xxl-6 { flex: 0 0 50%; max-width: 50%; }
    .col-xxl-12 { flex: 0 0 100%; max-width: 100%; }
}

/* ===== الشاشات الكبيرة (XL) ===== */
@media (max-width: 1199.98px) {
    .container-responsive {
        max-width: 1140px;
    }
    
    .col-xl-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-xl-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-xl-3 { flex: 0 0 25%; max-width: 25%; }
    .col-xl-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-xl-6 { flex: 0 0 50%; max-width: 50%; }
    .col-xl-12 { flex: 0 0 100%; max-width: 100%; }
}

/* ===== الشاشات المتوسطة الكبيرة (LG) ===== */
@media (max-width: 991.98px) {
    .container-responsive {
        max-width: 960px;
    }
    
    .col-lg-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-lg-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-lg-3 { flex: 0 0 25%; max-width: 25%; }
    .col-lg-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-lg-6 { flex: 0 0 50%; max-width: 50%; }
    .col-lg-12 { flex: 0 0 100%; max-width: 100%; }
    
    /* تحسينات للشاشات المتوسطة */
    .navbar-professional {
        padding: var(--space-md) 0;
    }
    
    .navbar-brand-professional {
        font-size: var(--font-size-xl);
    }
    
    .stats-card {
        margin-bottom: var(--space-lg);
    }
}

/* ===== الشاشات المتوسطة (MD) ===== */
@media (max-width: 767.98px) {
    .container-responsive {
        max-width: 720px;
    }
    
    .col-md-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-md-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-md-3 { flex: 0 0 25%; max-width: 25%; }
    .col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-md-6 { flex: 0 0 50%; max-width: 50%; }
    .col-md-12 { flex: 0 0 100%; max-width: 100%; }
    
    /* تحسينات للتابلت */
    .dashboard-header {
        padding: var(--space-xl) 0;
        margin-bottom: var(--space-xl);
    }
    
    .dashboard-title {
        font-size: var(--font-size-3xl);
    }
    
    .dashboard-subtitle {
        font-size: var(--font-size-base);
    }
    
    .grid-3,
    .grid-4 {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .stats-card .stats-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-xl);
    }
    
    .stats-card .stats-number {
        font-size: var(--font-size-2xl);
    }
}

/* ===== الشاشات الصغيرة (SM) ===== */
@media (max-width: 575.98px) {
    .container-responsive {
        max-width: 540px;
    }
    
    .col-sm-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .col-sm-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .col-sm-3 { flex: 0 0 25%; max-width: 25%; }
    .col-sm-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-sm-6 { flex: 0 0 50%; max-width: 50%; }
    .col-sm-12 { flex: 0 0 100%; max-width: 100%; }
    
    /* تحسينات للهواتف الذكية */
    .container-responsive,
    .container-fluid-responsive {
        padding-right: var(--space-md);
        padding-left: var(--space-md);
    }
    
    .navbar-professional {
        padding: var(--space-sm) 0;
    }
    
    .navbar-brand-professional {
        font-size: var(--font-size-lg);
        flex-direction: column;
        gap: var(--space-xs);
    }
    
    .navbar-brand-professional .login-logo {
        width: 40px;
        height: 40px;
    }
    
    .dashboard-header {
        padding: var(--space-lg) 0;
        margin-bottom: var(--space-lg);
        border-radius: 0 0 var(--radius-lg) var(--radius-lg);
    }
    
    .dashboard-title {
        font-size: var(--font-size-2xl);
        text-align: center;
    }
    
    .dashboard-subtitle {
        font-size: var(--font-size-sm);
        text-align: center;
    }
    
    .grid-2,
    .grid-3,
    .grid-4 {
        grid-template-columns: 1fr;
        gap: var(--space-md);
    }
    
    .stats-card {
        padding: var(--space-lg);
        margin-bottom: var(--space-md);
    }
    
    .stats-card .stats-icon {
        width: 45px;
        height: 45px;
        font-size: var(--font-size-lg);
    }
    
    .stats-card .stats-number {
        font-size: var(--font-size-xl);
    }
    
    .stats-card .stats-title {
        font-size: var(--font-size-sm);
    }
    
    .btn-advanced {
        width: 100%;
        margin-bottom: var(--space-sm);
    }
    
    .form-control-advanced {
        font-size: 16px; /* منع التكبير في iOS */
    }
    
    .tabs-nav {
        flex-direction: column;
        gap: 0;
    }
    
    .tab-link {
        text-align: center;
        border-radius: 0;
        border-bottom: 1px solid var(--light-gray);
    }
}

/* ===== الشاشات الصغيرة جداً (XS) ===== */
@media (max-width: 479.98px) {
    .container-responsive,
    .container-fluid-responsive {
        padding-right: var(--space-sm);
        padding-left: var(--space-sm);
    }
    
    .dashboard-title {
        font-size: var(--font-size-xl);
    }
    
    .stats-card {
        padding: var(--space-md);
    }
    
    .stats-card .stats-icon {
        width: 40px;
        height: 40px;
        font-size: var(--font-size-base);
    }
    
    .stats-card .stats-number {
        font-size: var(--font-size-lg);
    }
    
    .form-control-advanced {
        padding: var(--space-sm) var(--space-md);
    }
    
    .btn-advanced {
        padding: var(--space-sm) var(--space-lg);
        font-size: var(--font-size-sm);
    }
}

/* ===== تحسينات الأداء ===== */
/* تحسين الرسوم المتحركة للأجهزة الضعيفة */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* تحسين الطباعة */
@media print {
    .navbar-professional,
    .btn-advanced,
    .interactive-card::before {
        display: none !important;
    }
    
    .stats-card {
        box-shadow: none !important;
        border: 1px solid var(--medium-gray) !important;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
}

/* ===== فئات المساعدة المتجاوبة ===== */
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-grid { display: grid !important; }

@media (max-width: 767.98px) {
    .d-md-none { display: none !important; }
    .d-md-block { display: block !important; }
    .d-md-flex { display: flex !important; }
}

@media (max-width: 575.98px) {
    .d-sm-none { display: none !important; }
    .d-sm-block { display: block !important; }
    .d-sm-flex { display: flex !important; }
}

/* فئات النص المتجاوبة */
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-left { text-align: left !important; }

@media (max-width: 767.98px) {
    .text-md-center { text-align: center !important; }
}

@media (max-width: 575.98px) {
    .text-sm-center { text-align: center !important; }
}
