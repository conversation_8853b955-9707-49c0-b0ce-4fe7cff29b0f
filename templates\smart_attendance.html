{% extends "base.html" %}

{% block title %}نظام إدارة الموظفين - نظام الحضور الذكي{% endblock %}

{% block styles %}
<style>
    .attendance-header {
        background: linear-gradient(135deg, var(--accent-green) 0%, var(--secondary-teal) 50%, var(--primary-blue) 100%);
        color: var(--white);
        padding: var(--space-2xl) 0;
        margin-bottom: var(--space-2xl);
        border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);
        position: relative;
        overflow: hidden;
    }

    .attendance-header::before {
        content: '';
        position: absolute;
        top: -10%;
        right: -5%;
        width: 180px;
        height: 180px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='rgba(255,255,255,0.1)'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E") no-repeat center;
        background-size: contain;
        animation: attendanceFloat 10s ease-in-out infinite;
    }

    .attendance-methods {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: var(--space-xl);
        margin-bottom: var(--space-2xl);
    }

    .method-card {
        background: var(--white);
        border: var(--border-light);
        border-radius: var(--radius-2xl);
        box-shadow: var(--shadow-sm);
        padding: var(--space-2xl);
        transition: var(--transition-normal);
        position: relative;
        overflow: hidden;
        text-align: center;
    }

    .method-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 6px;
        background: linear-gradient(90deg, var(--method-color, var(--primary-blue)), var(--method-color-light, var(--primary-blue-light)));
    }

    .method-card:hover {
        transform: translateY(-12px);
        box-shadow: var(--shadow-xl);
    }

    .method-card.qr-code { --method-color: var(--primary-blue); --method-color-light: var(--primary-blue-light); }
    .method-card.face-recognition { --method-color: var(--accent-green); --method-color-light: var(--success); }
    .method-card.gps-tracking { --method-color: var(--warning); --method-color-light: var(--accent-orange); }
    .method-card.manual-entry { --method-color: var(--accent-purple); --method-color-light: #8b5cf6; }

    .method-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, var(--method-color, var(--primary-blue)), var(--method-color-light, var(--primary-blue-light)));
        color: var(--white);
        font-size: var(--font-size-3xl);
        margin: 0 auto var(--space-lg);
        box-shadow: var(--shadow-md);
    }

    .method-title {
        font-size: var(--font-size-xl);
        font-weight: var(--font-weight-bold);
        color: var(--near-black);
        margin-bottom: var(--space-md);
    }

    .method-description {
        font-size: var(--font-size-base);
        color: var(--dark-gray);
        line-height: 1.6;
        margin-bottom: var(--space-lg);
    }

    .method-features {
        list-style: none;
        padding: 0;
        margin-bottom: var(--space-lg);
    }

    .method-features li {
        display: flex;
        align-items: center;
        gap: var(--space-sm);
        padding: var(--space-xs) 0;
        font-size: var(--font-size-sm);
        color: var(--dark-gray);
    }

    .method-features li i {
        color: var(--method-color, var(--primary-blue));
        font-size: var(--font-size-sm);
    }

    .method-button {
        width: 100%;
        padding: var(--space-md) var(--space-lg);
        background: linear-gradient(135deg, var(--method-color, var(--primary-blue)), var(--method-color-light, var(--primary-blue-light)));
        color: var(--white);
        border: none;
        border-radius: var(--radius-xl);
        font-size: var(--font-size-base);
        font-weight: var(--font-weight-semibold);
        cursor: pointer;
        transition: var(--transition-normal);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--space-sm);
    }

    .method-button:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .attendance-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--space-lg);
        margin-bottom: var(--space-2xl);
    }

    .stat-card {
        background: var(--white);
        border: var(--border-light);
        border-radius: var(--radius-xl);
        box-shadow: var(--shadow-sm);
        padding: var(--space-lg);
        text-align: center;
        transition: var(--transition-normal);
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--stat-color, var(--primary-blue));
    }

    .stat-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-md);
    }

    .stat-card.present { --stat-color: var(--accent-green); }
    .stat-card.absent { --stat-color: var(--accent-red); }
    .stat-card.late { --stat-color: var(--warning); }
    .stat-card.early { --stat-color: var(--secondary-teal); }

    .stat-value {
        font-size: var(--font-size-3xl);
        font-weight: var(--font-weight-bold);
        color: var(--stat-color, var(--primary-blue));
        margin-bottom: var(--space-sm);
    }

    .stat-label {
        font-size: var(--font-size-base);
        font-weight: var(--font-weight-semibold);
        color: var(--near-black);
        margin-bottom: var(--space-xs);
    }

    .stat-change {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--space-xs);
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
    }

    .change-positive {
        color: var(--success);
    }

    .change-negative {
        color: var(--danger);
    }

    .attendance-log {
        background: var(--white);
        border: var(--border-light);
        border-radius: var(--radius-2xl);
        box-shadow: var(--shadow-sm);
        padding: var(--space-xl);
        margin-bottom: var(--space-2xl);
    }

    .log-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--space-lg);
        padding-bottom: var(--space-md);
        border-bottom: var(--border-light);
    }

    .log-title {
        font-size: var(--font-size-xl);
        font-weight: var(--font-weight-bold);
        color: var(--near-black);
    }

    .log-filters {
        display: flex;
        gap: var(--space-sm);
    }

    .filter-btn {
        padding: var(--space-sm) var(--space-md);
        border: 2px solid var(--medium-gray);
        border-radius: var(--radius-lg);
        background: var(--white);
        color: var(--dark-gray);
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
        cursor: pointer;
        transition: var(--transition-fast);
    }

    .filter-btn.active {
        background: var(--primary-blue);
        border-color: var(--primary-blue);
        color: var(--white);
    }

    .log-entries {
        display: flex;
        flex-direction: column;
        gap: var(--space-md);
    }

    .log-entry {
        display: flex;
        align-items: center;
        gap: var(--space-lg);
        padding: var(--space-md);
        border: var(--border-light);
        border-radius: var(--radius-lg);
        transition: var(--transition-fast);
    }

    .log-entry:hover {
        background: var(--off-white);
        border-color: var(--primary-blue);
    }

    .entry-status {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: var(--entry-color, var(--primary-blue));
    }

    .entry-status.present { --entry-color: var(--accent-green); }
    .entry-status.absent { --entry-color: var(--accent-red); }
    .entry-status.late { --entry-color: var(--warning); }

    .entry-info {
        flex: 1;
        display: grid;
        grid-template-columns: 2fr 1fr 1fr 1fr;
        gap: var(--space-md);
        align-items: center;
    }

    .entry-name {
        font-size: var(--font-size-base);
        font-weight: var(--font-weight-semibold);
        color: var(--near-black);
    }

    .entry-time {
        font-size: var(--font-size-sm);
        color: var(--dark-gray);
    }

    .entry-method {
        display: inline-flex;
        align-items: center;
        gap: var(--space-xs);
        padding: var(--space-xs) var(--space-sm);
        border-radius: var(--radius-full);
        font-size: var(--font-size-xs);
        font-weight: var(--font-weight-semibold);
        background: var(--method-bg, var(--light-gray));
        color: var(--method-text, var(--dark-gray));
    }

    .entry-method.qr { --method-bg: var(--primary-blue-light); --method-text: var(--primary-blue); }
    .entry-method.face { --method-bg: var(--success-light); --method-text: var(--success); }
    .entry-method.gps { --method-bg: var(--warning-light); --method-text: var(--warning); }

    .entry-location {
        font-size: var(--font-size-sm);
        color: var(--dark-gray);
    }

    .qr-scanner {
        background: var(--white);
        border: var(--border-light);
        border-radius: var(--radius-2xl);
        box-shadow: var(--shadow-sm);
        padding: var(--space-2xl);
        text-align: center;
        margin-bottom: var(--space-2xl);
    }

    .scanner-area {
        width: 300px;
        height: 300px;
        border: 3px dashed var(--primary-blue);
        border-radius: var(--radius-xl);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto var(--space-lg);
        background: var(--off-white);
        position: relative;
        overflow: hidden;
    }

    .scanner-area::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 100%;
        background: linear-gradient(180deg, transparent 0%, var(--primary-blue-light) 50%, transparent 100%);
        animation: scanLine 2s infinite;
        opacity: 0.3;
    }

    .scanner-icon {
        font-size: var(--font-size-4xl);
        color: var(--primary-blue);
    }

    @keyframes attendanceFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        25% { transform: translateY(-10px) rotate(2deg); }
        50% { transform: translateY(-5px) rotate(-2deg); }
        75% { transform: translateY(-15px) rotate(1deg); }
    }

    @keyframes scanLine {
        0% { transform: translateY(-100%); }
        100% { transform: translateY(300px); }
    }

    /* التصميم المتجاوب */
    @media (max-width: 1200px) {
        .attendance-methods {
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        }
    }

    @media (max-width: 768px) {
        .attendance-methods {
            grid-template-columns: 1fr;
        }

        .attendance-stats {
            grid-template-columns: repeat(2, 1fr);
        }

        .log-filters {
            flex-wrap: wrap;
        }

        .entry-info {
            grid-template-columns: 1fr;
            gap: var(--space-sm);
        }

        .scanner-area {
            width: 250px;
            height: 250px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-professional">
    <!-- رأس صفحة الحضور الذكي -->
    <div class="attendance-header">
        <div class="container-professional">
            <div class="text-center">
                <div class="dashboard-icon">
                    <i class="fas fa-user-check"></i>
                </div>
                <h1 class="dashboard-title">نظام الحضور والانصراف الذكي</h1>
                <p class="dashboard-subtitle">نظام متطور للحضور مع تقنيات GPS وبصمة الوجه ورموز QR</p>
            </div>
        </div>
    </div>

    <!-- إحصائيات الحضور -->
    <div class="attendance-stats">
        <div class="stat-card present">
            <div class="stat-value">234</div>
            <div class="stat-label">حاضر اليوم</div>
            <div class="stat-change change-positive">
                <i class="fas fa-arrow-up"></i>
                +3.2%
            </div>
        </div>

        <div class="stat-card absent">
            <div class="stat-value">13</div>
            <div class="stat-label">غائب اليوم</div>
            <div class="stat-change change-negative">
                <i class="fas fa-arrow-down"></i>
                -1.5%
            </div>
        </div>

        <div class="stat-card late">
            <div class="stat-value">8</div>
            <div class="stat-label">متأخر</div>
            <div class="stat-change change-negative">
                <i class="fas fa-arrow-down"></i>
                -2.1%
            </div>
        </div>

        <div class="stat-card early">
            <div class="stat-value">45</div>
            <div class="stat-label">مبكر</div>
            <div class="stat-change change-positive">
                <i class="fas fa-arrow-up"></i>
                +5.8%
            </div>
        </div>
    </div>

    <!-- طرق تسجيل الحضور -->
    <div class="attendance-methods">
        <div class="method-card qr-code">
            <div class="method-icon">
                <i class="fas fa-qrcode"></i>
            </div>
            <h3 class="method-title">رمز QR</h3>
            <p class="method-description">
                امسح رمز QR الخاص بك لتسجيل الحضور والانصراف بسرعة وسهولة
            </p>
            <ul class="method-features">
                <li><i class="fas fa-check"></i> سريع وآمن</li>
                <li><i class="fas fa-check"></i> لا يتطلب اتصال بالإنترنت</li>
                <li><i class="fas fa-check"></i> دقة عالية</li>
            </ul>
            <button class="method-button" onclick="openQRScanner()">
                <i class="fas fa-camera"></i>
                فتح الماسح الضوئي
            </button>
        </div>

        <div class="method-card face-recognition">
            <div class="method-icon">
                <i class="fas fa-user-circle"></i>
            </div>
            <h3 class="method-title">بصمة الوجه</h3>
            <p class="method-description">
                استخدم تقنية التعرف على الوجه المتطورة لتسجيل حضورك تلقائياً
            </p>
            <ul class="method-features">
                <li><i class="fas fa-check"></i> تقنية متطورة</li>
                <li><i class="fas fa-check"></i> أمان عالي</li>
                <li><i class="fas fa-check"></i> تسجيل تلقائي</li>
            </ul>
            <button class="method-button" onclick="startFaceRecognition()">
                <i class="fas fa-video"></i>
                بدء التعرف على الوجه
            </button>
        </div>

        <div class="method-card gps-tracking">
            <div class="method-icon">
                <i class="fas fa-map-marker-alt"></i>
            </div>
            <h3 class="method-title">تتبع GPS</h3>
            <p class="method-description">
                تسجيل الحضور تلقائياً عند الوصول إلى موقع العمل باستخدام GPS
            </p>
            <ul class="method-features">
                <li><i class="fas fa-check"></i> تتبع الموقع</li>
                <li><i class="fas fa-check"></i> تسجيل تلقائي</li>
                <li><i class="fas fa-check"></i> دقة جغرافية</li>
            </ul>
            <button class="method-button" onclick="enableGPSTracking()">
                <i class="fas fa-location-arrow"></i>
                تفعيل تتبع GPS
            </button>
        </div>

        <div class="method-card manual-entry">
            <div class="method-icon">
                <i class="fas fa-keyboard"></i>
            </div>
            <h3 class="method-title">إدخال يدوي</h3>
            <p class="method-description">
                تسجيل الحضور والانصراف يدوياً في حالات خاصة أو عند وجود مشاكل تقنية
            </p>
            <ul class="method-features">
                <li><i class="fas fa-check"></i> مرونة عالية</li>
                <li><i class="fas fa-check"></i> حل بديل</li>
                <li><i class="fas fa-check"></i> سهولة الاستخدام</li>
            </ul>
            <button class="method-button" onclick="openManualEntry()">
                <i class="fas fa-edit"></i>
                إدخال يدوي
            </button>
        </div>
    </div>

    <!-- ماسح رمز QR -->
    <div class="qr-scanner" id="qrScanner" style="display: none;">
        <h3 class="method-title">ماسح رمز QR</h3>
        <div class="scanner-area">
            <i class="fas fa-qrcode scanner-icon"></i>
        </div>
        <p>وجه الكاميرا نحو رمز QR لتسجيل الحضور</p>
        <button class="method-button" onclick="closeQRScanner()" style="background: var(--accent-red); max-width: 200px; margin: 0 auto;">
            <i class="fas fa-times"></i>
            إغلاق الماسح
        </button>
    </div>

    <!-- سجل الحضور -->
    <div class="attendance-log">
        <div class="log-header">
            <h3 class="log-title">سجل الحضور اليومي</h3>
            <div class="log-filters">
                <button class="filter-btn active">اليوم</button>
                <button class="filter-btn">الأسبوع</button>
                <button class="filter-btn">الشهر</button>
            </div>
        </div>

        <div class="log-entries">
            <div class="log-entry">
                <div class="entry-status present"></div>
                <div class="entry-info">
                    <div class="entry-name">أحمد محمد علي</div>
                    <div class="entry-time">08:15 ص</div>
                    <div class="entry-method qr">
                        <i class="fas fa-qrcode"></i>
                        QR Code
                    </div>
                    <div class="entry-location">المكتب الرئيسي</div>
                </div>
            </div>

            <div class="log-entry">
                <div class="entry-status late"></div>
                <div class="entry-info">
                    <div class="entry-name">سارة أحمد محمود</div>
                    <div class="entry-time">08:45 ص</div>
                    <div class="entry-method face">
                        <i class="fas fa-user-circle"></i>
                        Face ID
                    </div>
                    <div class="entry-location">المكتب الرئيسي</div>
                </div>
            </div>

            <div class="log-entry">
                <div class="entry-status present"></div>
                <div class="entry-info">
                    <div class="entry-name">محمد عبدالله حسن</div>
                    <div class="entry-time">08:00 ص</div>
                    <div class="entry-method gps">
                        <i class="fas fa-map-marker-alt"></i>
                        GPS
                    </div>
                    <div class="entry-location">الفرع الشمالي</div>
                </div>
            </div>

            <div class="log-entry">
                <div class="entry-status present"></div>
                <div class="entry-info">
                    <div class="entry-name">فاطمة علي أحمد</div>
                    <div class="entry-time">07:55 ص</div>
                    <div class="entry-method qr">
                        <i class="fas fa-qrcode"></i>
                        QR Code
                    </div>
                    <div class="entry-location">المكتب الرئيسي</div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// دوال تفعيل طرق الحضور المختلفة
function openQRScanner() {
    document.getElementById('qrScanner').style.display = 'block';
    document.getElementById('qrScanner').scrollIntoView({ behavior: 'smooth' });
}

function closeQRScanner() {
    document.getElementById('qrScanner').style.display = 'none';
}

function startFaceRecognition() {
    alert('سيتم تفعيل كاميرا التعرف على الوجه');
}

function enableGPSTracking() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(function(position) {
            alert(`تم تحديد موقعك: ${position.coords.latitude}, ${position.coords.longitude}`);
        });
    } else {
        alert('المتصفح لا يدعم تحديد الموقع');
    }
}

function openManualEntry() {
    alert('سيتم فتح نافذة الإدخال اليدوي');
}

// تفعيل أزرار التصفية
document.querySelectorAll('.filter-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
        this.classList.add('active');
        console.log('تم تغيير التصفية إلى:', this.textContent);
    });
});

// تحديث الوقت الحالي
function updateCurrentTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('ar-SA');
    console.log('الوقت الحالي:', timeString);
}

setInterval(updateCurrentTime, 1000);
</script>
{% endblock %}
