/**
 * ALEMIS Enhanced UI JavaScript
 * Modern, Interactive, and Accessible Components
 */

class ALEMISEnhancedUI {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeComponents();
        this.setupAnimations();
        this.setupAccessibility();
    }

    setupEventListeners() {
        document.addEventListener('DOMContentLoaded', () => {
            this.initializeTooltips();
            this.initializeModals();
            this.initializeDropdowns();
            this.initializeForms();
        });
    }

    initializeComponents() {
        // Initialize enhanced cards
        this.initializeCards();
        
        // Initialize statistics counters
        this.initializeCounters();
        
        // Initialize charts if available
        this.initializeCharts();
        
        // Initialize data tables
        this.initializeDataTables();
    }

    initializeCards() {
        const cards = document.querySelectorAll('.card-enhanced');
        cards.forEach(card => {
            // Add hover effects
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-4px)';
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0)';
            });
        });
    }

    initializeCounters() {
        const counters = document.querySelectorAll('.stat-number');
        
        const observerOptions = {
            threshold: 0.5,
            rootMargin: '0px 0px -100px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.animateCounter(entry.target);
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        counters.forEach(counter => {
            observer.observe(counter);
        });
    }

    animateCounter(element) {
        const target = parseInt(element.textContent);
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;

        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                element.textContent = target;
                clearInterval(timer);
            } else {
                element.textContent = Math.floor(current);
            }
        }, 16);
    }

    initializeTooltips() {
        // Initialize Bootstrap tooltips if available
        if (typeof bootstrap !== 'undefined') {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    }

    initializeModals() {
        // Enhanced modal functionality
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            modal.addEventListener('show.bs.modal', () => {
                document.body.style.overflow = 'hidden';
            });
            
            modal.addEventListener('hidden.bs.modal', () => {
                document.body.style.overflow = 'auto';
            });
        });
    }

    initializeDropdowns() {
        const dropdowns = document.querySelectorAll('.dropdown-enhanced');
        dropdowns.forEach(dropdown => {
            const toggle = dropdown.querySelector('.dropdown-toggle');
            const menu = dropdown.querySelector('.dropdown-menu');
            
            if (toggle && menu) {
                toggle.addEventListener('click', (e) => {
                    e.preventDefault();
                    menu.classList.toggle('show');
                });
                
                // Close dropdown when clicking outside
                document.addEventListener('click', (e) => {
                    if (!dropdown.contains(e.target)) {
                        menu.classList.remove('show');
                    }
                });
            }
        });
    }

    initializeForms() {
        const forms = document.querySelectorAll('.form-enhanced');
        forms.forEach(form => {
            // Add floating labels effect
            const inputs = form.querySelectorAll('.form-control');
            inputs.forEach(input => {
                this.setupFloatingLabel(input);
                this.setupValidation(input);
            });
        });
    }

    setupFloatingLabel(input) {
        const label = input.previousElementSibling;
        if (label && label.classList.contains('form-label')) {
            input.addEventListener('focus', () => {
                label.style.transform = 'translateY(-20px) scale(0.8)';
                label.style.color = 'var(--primary-500)';
            });
            
            input.addEventListener('blur', () => {
                if (!input.value) {
                    label.style.transform = 'translateY(0) scale(1)';
                    label.style.color = 'var(--gray-600)';
                }
            });
        }
    }

    setupValidation(input) {
        input.addEventListener('blur', () => {
            this.validateField(input);
        });
        
        input.addEventListener('input', () => {
            if (input.classList.contains('is-invalid')) {
                this.validateField(input);
            }
        });
    }

    validateField(input) {
        const value = input.value.trim();
        const type = input.type;
        let isValid = true;
        let message = '';

        // Basic validation rules
        if (input.hasAttribute('required') && !value) {
            isValid = false;
            message = 'هذا الحقل مطلوب';
        } else if (type === 'email' && value && !this.isValidEmail(value)) {
            isValid = false;
            message = 'يرجى إدخال بريد إلكتروني صحيح';
        } else if (type === 'tel' && value && !this.isValidPhone(value)) {
            isValid = false;
            message = 'يرجى إدخال رقم هاتف صحيح';
        }

        this.updateFieldValidation(input, isValid, message);
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    isValidPhone(phone) {
        const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
        return phoneRegex.test(phone);
    }

    updateFieldValidation(input, isValid, message) {
        const feedback = input.parentNode.querySelector('.invalid-feedback') || 
                        this.createFeedbackElement(input.parentNode);

        if (isValid) {
            input.classList.remove('is-invalid');
            input.classList.add('is-valid');
            feedback.textContent = '';
            feedback.style.display = 'none';
        } else {
            input.classList.remove('is-valid');
            input.classList.add('is-invalid');
            feedback.textContent = message;
            feedback.style.display = 'block';
        }
    }

    createFeedbackElement(parent) {
        const feedback = document.createElement('div');
        feedback.className = 'invalid-feedback';
        feedback.style.display = 'none';
        parent.appendChild(feedback);
        return feedback;
    }

    setupAnimations() {
        // Intersection Observer for animations
        const animatedElements = document.querySelectorAll('.fade-in, .slide-in-right');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0) translateX(0)';
                }
            });
        }, { threshold: 0.1 });

        animatedElements.forEach(el => {
            el.style.opacity = '0';
            if (el.classList.contains('slide-in-right')) {
                el.style.transform = 'translateX(30px)';
            } else {
                el.style.transform = 'translateY(20px)';
            }
            observer.observe(el);
        });
    }

    setupAccessibility() {
        // Keyboard navigation for dropdowns
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const openDropdowns = document.querySelectorAll('.dropdown-menu.show');
                openDropdowns.forEach(menu => menu.classList.remove('show'));
            }
        });

        // Focus management for modals
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            modal.addEventListener('shown.bs.modal', () => {
                const firstInput = modal.querySelector('input, button, select, textarea');
                if (firstInput) firstInput.focus();
            });
        });
    }

    initializeCharts() {
        // Chart.js integration if available
        if (typeof Chart !== 'undefined') {
            const chartElements = document.querySelectorAll('[data-chart]');
            chartElements.forEach(element => {
                this.createChart(element);
            });
        }
    }

    createChart(element) {
        const type = element.dataset.chart;
        const data = JSON.parse(element.dataset.chartData || '{}');
        
        const ctx = element.getContext('2d');
        new Chart(ctx, {
            type: type,
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    }
                },
                scales: type !== 'pie' && type !== 'doughnut' ? {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                } : {}
            }
        });
    }

    initializeDataTables() {
        // DataTables integration if available
        if (typeof $ !== 'undefined' && $.fn.DataTable) {
            $('.table-enhanced').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/ar.json'
                },
                responsive: true,
                pageLength: 25,
                dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                     '<"row"<"col-sm-12"tr>>' +
                     '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
            });
        }
    }

    // Utility methods
    showNotification(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-enhanced notification`;
        notification.innerHTML = `
            <i class="fas fa-${this.getIconForType(type)} me-2"></i>
            ${message}
            <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
        `;
        
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            animation: slideInRight 0.3s ease-out;
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, duration);
    }

    getIconForType(type) {
        const icons = {
            success: 'check-circle',
            warning: 'exclamation-triangle',
            danger: 'times-circle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    showLoading(element) {
        element.classList.add('loading');
        element.style.pointerEvents = 'none';
    }

    hideLoading(element) {
        element.classList.remove('loading');
        element.style.pointerEvents = 'auto';
    }
}

// Initialize the enhanced UI when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.alemisUI = new ALEMISEnhancedUI();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ALEMISEnhancedUI;
}
