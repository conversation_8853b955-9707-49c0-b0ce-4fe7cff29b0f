"""
ALEMIS - نظام إدارة إجازات الموظفين في المختبرات
Enhanced Laboratory Employee Leave Management System

وحدات النظام المحسنة
"""

__version__ = "2.0.0-enhanced"
__author__ = "ALEMIS Development Team"
__description__ = "نظام إدارة إجازات الموظفين في المختبرات المحسن"

# تصدير الفئات الرئيسية
try:
    from .security_enhanced import SecurityManager
    from .cache import CacheManager
    from .reports_enhanced import ReportsManager
    from .database import DatabaseManager
    from .models import User, Department, LeaveType
    
    __all__ = [
        'SecurityManager',
        'CacheManager', 
        'ReportsManager',
        'DatabaseManager',
        'User',
        'Department',
        'LeaveType'
    ]
    
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ تحذير: بعض الوحدات غير متاحة: {e}")
    __all__ = []
    MODULES_AVAILABLE = False
