/* ===== ALEMIS Enhanced UI Styles ===== */
/* Modern, Professional, and Responsive Design */

/* ===== CSS Variables for Consistent Theming ===== */
:root {
    /* Primary Colors - Blue Theme */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;

    /* Secondary Colors - Red Accent */
    --secondary-50: #fef2f2;
    --secondary-100: #fee2e2;
    --secondary-200: #fecaca;
    --secondary-300: #fca5a5;
    --secondary-400: #f87171;
    --secondary-500: #ef4444;
    --secondary-600: #dc2626;
    --secondary-700: #b91c1c;
    --secondary-800: #991b1b;
    --secondary-900: #7f1d1d;

    /* Neutral Colors */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* Success, Warning, Error */
    --success-500: #10b981;
    --warning-500: #f59e0b;
    --error-500: #ef4444;

    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;

    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;
}

/* ===== Base Styles ===== */
* {
    box-sizing: border-box;
}

body {
    font-family: 'Tajawal', 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--primary-50) 100%);
    color: var(--gray-800);
    line-height: 1.6;
    direction: rtl;
    text-align: right;
    min-height: 100vh;
}

/* ===== Enhanced Cards ===== */
.card-enhanced {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
    overflow: hidden;
}

.card-enhanced:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.card-enhanced .card-header {
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
    color: white;
    padding: var(--space-4) var(--space-6);
    border-bottom: none;
}

.card-enhanced .card-body {
    padding: var(--space-6);
}

/* ===== Enhanced Buttons ===== */
.btn-enhanced {
    border-radius: var(--radius-lg);
    font-weight: 600;
    padding: var(--space-3) var(--space-6);
    transition: all var(--transition-fast);
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
}

.btn-enhanced:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-enhanced.btn-primary {
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
    color: white;
}

.btn-enhanced.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
}

.btn-enhanced.btn-secondary {
    background: linear-gradient(135deg, var(--secondary-500) 0%, var(--secondary-600) 100%);
    color: white;
}

.btn-enhanced.btn-success {
    background: linear-gradient(135deg, var(--success-500) 0%, #059669 100%);
    color: white;
}

/* ===== Enhanced Forms ===== */
.form-enhanced .form-control {
    border-radius: var(--radius-lg);
    border: 2px solid var(--gray-200);
    padding: var(--space-3) var(--space-4);
    transition: all var(--transition-fast);
    background: white;
}

.form-enhanced .form-control:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
    outline: none;
}

.form-enhanced .form-label {
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: var(--space-2);
}

/* ===== Enhanced Navigation ===== */
.navbar-enhanced {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
    box-shadow: var(--shadow-lg);
    padding: var(--space-4) 0;
}

.navbar-enhanced .navbar-brand {
    font-weight: 800;
    font-size: 1.5rem;
    color: white !important;
}

.navbar-enhanced .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: var(--space-2) var(--space-4) !important;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.navbar-enhanced .nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white !important;
}

/* ===== Enhanced Dropdowns ===== */
.dropdown-enhanced .dropdown-menu {
    border-radius: var(--radius-lg);
    border: none;
    box-shadow: var(--shadow-xl);
    padding: var(--space-2);
    margin-top: var(--space-2);
}

.dropdown-enhanced .dropdown-item {
    border-radius: var(--radius-md);
    padding: var(--space-3) var(--space-4);
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.dropdown-enhanced .dropdown-item:hover {
    background: var(--primary-50);
    color: var(--primary-700);
}

/* ===== Enhanced Tables ===== */
.table-enhanced {
    background: white;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.table-enhanced thead th {
    background: var(--gray-50);
    border: none;
    font-weight: 600;
    color: var(--gray-700);
    padding: var(--space-4);
}

.table-enhanced tbody td {
    border: none;
    padding: var(--space-4);
    border-bottom: 1px solid var(--gray-100);
}

.table-enhanced tbody tr:hover {
    background: var(--gray-50);
}

/* ===== Enhanced Alerts ===== */
.alert-enhanced {
    border-radius: var(--radius-lg);
    border: none;
    padding: var(--space-4) var(--space-6);
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.alert-enhanced.alert-success {
    background: var(--success-500);
    color: white;
}

.alert-enhanced.alert-warning {
    background: var(--warning-500);
    color: white;
}

.alert-enhanced.alert-danger {
    background: var(--error-500);
    color: white;
}

/* ===== Enhanced Statistics Cards ===== */
.stat-card {
    background: white;
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: var(--shadow-md);
    border-left: 4px solid var(--primary-500);
    transition: all var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.stat-card .stat-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: var(--space-4);
}

.stat-card .stat-number {
    font-size: 2rem;
    font-weight: 800;
    color: var(--gray-800);
    margin-bottom: var(--space-2);
}

.stat-card .stat-label {
    color: var(--gray-600);
    font-weight: 500;
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
    .card-enhanced .card-body {
        padding: var(--space-4);
    }
    
    .btn-enhanced {
        padding: var(--space-2) var(--space-4);
        font-size: 0.875rem;
    }
    
    .stat-card {
        padding: var(--space-4);
    }
    
    .stat-card .stat-number {
        font-size: 1.5rem;
    }
}

/* ===== Dark Mode Support ===== */
@media (prefers-color-scheme: dark) {
    :root {
        --gray-50: #1f2937;
        --gray-100: #374151;
        --gray-200: #4b5563;
        --gray-800: #f9fafb;
        --gray-900: #ffffff;
    }
    
    body {
        background: linear-gradient(135deg, var(--gray-900) 0%, var(--gray-800) 100%);
        color: var(--gray-100);
    }
    
    .card-enhanced {
        background: var(--gray-800);
        border-color: var(--gray-700);
    }
    
    .form-enhanced .form-control {
        background: var(--gray-800);
        border-color: var(--gray-600);
        color: var(--gray-100);
    }
}

/* ===== Animation Classes ===== */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-in-out;
}

@keyframes slideInRight {
    from { opacity: 0; transform: translateX(30px); }
    to { opacity: 1; transform: translateX(0); }
}

/* ===== Loading States ===== */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}
