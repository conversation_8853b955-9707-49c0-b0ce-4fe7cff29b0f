{% extends "base.html" %}

{% block title %}نظام إدارة الموظفين - إدارة المشاريع{% endblock %}

{% block styles %}
<style>
    .projects-header {
        background: linear-gradient(135deg, var(--secondary-teal) 0%, var(--primary-blue) 50%, var(--accent-purple) 100%);
        color: var(--white);
        padding: var(--space-2xl) 0;
        margin-bottom: var(--space-2xl);
        border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);
        position: relative;
        overflow: hidden;
    }

    .projects-header::before {
        content: '';
        position: absolute;
        top: -15%;
        right: -8%;
        width: 200px;
        height: 200px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='rgba(255,255,255,0.1)'%3E%3Cpath d='M19 8h-2v3h-3v2h3v3h2v-3h3v-2h-3V8zM4 8h2v8H4V8zm3 0h2v8H7V8zm3 0h2v8h-2V8z'/%3E%3C/svg%3E") no-repeat center;
        background-size: contain;
        animation: projectFloat 12s ease-in-out infinite;
    }

    .project-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--space-lg);
        margin-bottom: var(--space-2xl);
    }

    .stat-card {
        background: var(--white);
        border: var(--border-light);
        border-radius: var(--radius-2xl);
        box-shadow: var(--shadow-sm);
        padding: var(--space-xl);
        transition: var(--transition-normal);
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(90deg, var(--stat-color, var(--primary-blue)), var(--stat-color-light, var(--primary-blue-light)));
    }

    .stat-card:hover {
        transform: translateY(-8px);
        box-shadow: var(--shadow-xl);
    }

    .stat-card.active { --stat-color: var(--accent-green); --stat-color-light: var(--success); }
    .stat-card.pending { --stat-color: var(--warning); --stat-color-light: var(--accent-orange); }
    .stat-card.completed { --stat-color: var(--primary-blue); --stat-color-light: var(--primary-blue-light); }
    .stat-card.overdue { --stat-color: var(--accent-red); --stat-color-light: var(--danger); }

    .stat-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--space-lg);
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: var(--radius-xl);
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, var(--stat-color, var(--primary-blue)), var(--stat-color-light, var(--primary-blue-light)));
        color: var(--white);
        font-size: var(--font-size-xl);
    }

    .stat-value {
        font-size: var(--font-size-3xl);
        font-weight: var(--font-weight-bold);
        color: var(--stat-color, var(--primary-blue));
    }

    .stat-label {
        font-size: var(--font-size-base);
        font-weight: var(--font-weight-semibold);
        color: var(--near-black);
        margin-bottom: var(--space-xs);
    }

    .stat-description {
        font-size: var(--font-size-sm);
        color: var(--dark-gray);
    }

    .projects-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: var(--space-xl);
        margin-bottom: var(--space-2xl);
    }

    .project-card {
        background: var(--white);
        border: var(--border-light);
        border-radius: var(--radius-2xl);
        box-shadow: var(--shadow-sm);
        overflow: hidden;
        transition: var(--transition-normal);
        position: relative;
    }

    .project-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 6px;
        background: linear-gradient(90deg, var(--project-color, var(--primary-blue)), var(--project-color-light, var(--primary-blue-light)));
    }

    .project-card:hover {
        transform: translateY(-8px);
        box-shadow: var(--shadow-xl);
    }

    .project-card.high-priority { --project-color: var(--accent-red); --project-color-light: var(--danger); }
    .project-card.medium-priority { --project-color: var(--warning); --project-color-light: var(--accent-orange); }
    .project-card.low-priority { --project-color: var(--accent-green); --project-color-light: var(--success); }

    .project-header {
        padding: var(--space-xl);
        border-bottom: var(--border-light);
    }

    .project-title {
        font-size: var(--font-size-xl);
        font-weight: var(--font-weight-bold);
        color: var(--near-black);
        margin-bottom: var(--space-sm);
    }

    .project-description {
        font-size: var(--font-size-sm);
        color: var(--dark-gray);
        margin-bottom: var(--space-md);
        line-height: 1.6;
    }

    .project-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--space-md);
    }

    .project-status {
        display: inline-flex;
        align-items: center;
        gap: var(--space-xs);
        padding: var(--space-xs) var(--space-sm);
        border-radius: var(--radius-full);
        font-size: var(--font-size-xs);
        font-weight: var(--font-weight-semibold);
        text-transform: uppercase;
    }

    .status-active {
        background: var(--success-light);
        color: var(--success);
    }

    .status-pending {
        background: var(--warning-light);
        color: var(--warning);
    }

    .status-completed {
        background: var(--info-light);
        color: var(--info);
    }

    .project-priority {
        display: inline-flex;
        align-items: center;
        gap: var(--space-xs);
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
        color: var(--project-color, var(--primary-blue));
    }

    .project-progress {
        margin-bottom: var(--space-md);
    }

    .progress-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--space-sm);
    }

    .progress-label {
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
        color: var(--dark-gray);
    }

    .progress-percentage {
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-bold);
        color: var(--project-color, var(--primary-blue));
    }

    .progress-bar {
        height: 8px;
        background: var(--light-gray);
        border-radius: var(--radius-full);
        overflow: hidden;
        position: relative;
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, var(--project-color, var(--primary-blue)), var(--project-color-light, var(--primary-blue-light)));
        border-radius: var(--radius-full);
        transition: width 1s ease-in-out;
        position: relative;
    }

    .progress-fill::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
        animation: progressShimmer 2s infinite;
    }

    .project-team {
        display: flex;
        align-items: center;
        gap: var(--space-sm);
        margin-bottom: var(--space-md);
    }

    .team-label {
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
        color: var(--dark-gray);
        margin-right: var(--space-sm);
    }

    .team-avatars {
        display: flex;
        gap: var(--space-xs);
    }

    .team-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-blue), var(--secondary-teal));
        color: var(--white);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: var(--font-size-xs);
        font-weight: var(--font-weight-bold);
        border: 2px solid var(--white);
        box-shadow: var(--shadow-sm);
    }

    .project-footer {
        padding: var(--space-lg) var(--space-xl);
        background: var(--off-white);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .project-deadline {
        display: flex;
        align-items: center;
        gap: var(--space-xs);
        font-size: var(--font-size-sm);
        color: var(--dark-gray);
    }

    .project-actions {
        display: flex;
        gap: var(--space-sm);
    }

    .btn-project {
        padding: var(--space-sm) var(--space-md);
        border-radius: var(--radius-lg);
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
        text-decoration: none;
        transition: var(--transition-fast);
        display: inline-flex;
        align-items: center;
        gap: var(--space-xs);
        border: none;
        cursor: pointer;
    }

    .btn-view {
        background: var(--primary-blue);
        color: var(--white);
    }

    .btn-view:hover {
        background: var(--primary-blue-dark);
        color: var(--white);
        text-decoration: none;
    }

    .btn-edit {
        background: var(--white);
        color: var(--warning);
        border: 2px solid var(--warning);
    }

    .btn-edit:hover {
        background: var(--warning);
        color: var(--white);
        text-decoration: none;
    }

    .add-project-btn {
        position: fixed;
        bottom: var(--space-2xl);
        right: var(--space-2xl);
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--secondary-teal), var(--primary-blue));
        color: var(--white);
        border: none;
        font-size: var(--font-size-2xl);
        cursor: pointer;
        box-shadow: var(--shadow-lg);
        transition: var(--transition-normal);
        z-index: 1000;
    }

    .add-project-btn:hover {
        transform: scale(1.1);
        box-shadow: var(--shadow-xl);
    }

    @keyframes projectFloat {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        25% { transform: translateY(-15px) rotate(3deg); }
        50% { transform: translateY(-8px) rotate(-3deg); }
        75% { transform: translateY(-20px) rotate(2deg); }
    }

    @keyframes progressShimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    /* التصميم المتجاوب */
    @media (max-width: 768px) {
        .project-stats {
            grid-template-columns: 1fr;
        }

        .projects-grid {
            grid-template-columns: 1fr;
        }

        .project-meta {
            flex-direction: column;
            align-items: flex-start;
            gap: var(--space-sm);
        }

        .project-footer {
            flex-direction: column;
            gap: var(--space-md);
            align-items: flex-start;
        }

        .add-project-btn {
            bottom: var(--space-lg);
            right: var(--space-lg);
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-professional">
    <!-- رأس صفحة المشاريع -->
    <div class="projects-header">
        <div class="container-professional">
            <div class="text-center">
                <div class="dashboard-icon">
                    <i class="fas fa-project-diagram"></i>
                </div>
                <h1 class="dashboard-title">إدارة المشاريع</h1>
                <p class="dashboard-subtitle">نظام شامل لإدارة المشاريع وتتبع المهام والتعاون بين الأقسام</p>
            </div>
        </div>
    </div>

    <!-- إحصائيات المشاريع -->
    <div class="project-stats">
        <div class="stat-card active">
            <div class="stat-header">
                <div class="stat-icon">
                    <i class="fas fa-play"></i>
                </div>
                <div class="stat-value">18</div>
            </div>
            <div class="stat-label">المشاريع النشطة</div>
            <div class="stat-description">قيد التنفيذ حالياً</div>
        </div>

        <div class="stat-card pending">
            <div class="stat-header">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-value">7</div>
            </div>
            <div class="stat-label">في الانتظار</div>
            <div class="stat-description">تحتاج إلى موافقة</div>
        </div>

        <div class="stat-card completed">
            <div class="stat-header">
                <div class="stat-icon">
                    <i class="fas fa-check"></i>
                </div>
                <div class="stat-value">42</div>
            </div>
            <div class="stat-label">مكتملة</div>
            <div class="stat-description">تم إنجازها بنجاح</div>
        </div>

        <div class="stat-card overdue">
            <div class="stat-header">
                <div class="stat-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-value">3</div>
            </div>
            <div class="stat-label">متأخرة</div>
            <div class="stat-description">تجاوزت الموعد النهائي</div>
        </div>
    </div>

    <!-- شبكة المشاريع -->
    <div class="projects-grid">
        <!-- مشروع عالي الأولوية -->
        <div class="project-card high-priority">
            <div class="project-header">
                <h3 class="project-title">تطوير نظام إدارة المخزون</h3>
                <p class="project-description">
                    تطوير نظام شامل لإدارة المخزون مع تتبع المواد والمعدات وإدارة المشتريات والموردين.
                </p>
                <div class="project-meta">
                    <div class="project-status status-active">
                        <i class="fas fa-play"></i>
                        نشط
                    </div>
                    <div class="project-priority">
                        <i class="fas fa-exclamation"></i>
                        أولوية عالية
                    </div>
                </div>
                <div class="project-progress">
                    <div class="progress-header">
                        <span class="progress-label">التقدم</span>
                        <span class="progress-percentage">65%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 65%;"></div>
                    </div>
                </div>
                <div class="project-team">
                    <span class="team-label">الفريق:</span>
                    <div class="team-avatars">
                        <div class="team-avatar">أح</div>
                        <div class="team-avatar">سم</div>
                        <div class="team-avatar">فا</div>
                        <div class="team-avatar">+2</div>
                    </div>
                </div>
            </div>
            <div class="project-footer">
                <div class="project-deadline">
                    <i class="fas fa-calendar"></i>
                    الموعد النهائي: 15 أكتوبر 2025
                </div>
                <div class="project-actions">
                    <button class="btn-project btn-view">
                        <i class="fas fa-eye"></i>
                        عرض
                    </button>
                    <button class="btn-project btn-edit">
                        <i class="fas fa-edit"></i>
                        تعديل
                    </button>
                </div>
            </div>
        </div>

        <!-- مشروع متوسط الأولوية -->
        <div class="project-card medium-priority">
            <div class="project-header">
                <h3 class="project-title">تحديث نظام الموارد البشرية</h3>
                <p class="project-description">
                    تحديث وتطوير نظام الموارد البشرية الحالي مع إضافة ميزات جديدة وتحسين الأداء.
                </p>
                <div class="project-meta">
                    <div class="project-status status-active">
                        <i class="fas fa-play"></i>
                        نشط
                    </div>
                    <div class="project-priority">
                        <i class="fas fa-minus"></i>
                        أولوية متوسطة
                    </div>
                </div>
                <div class="project-progress">
                    <div class="progress-header">
                        <span class="progress-label">التقدم</span>
                        <span class="progress-percentage">42%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 42%;"></div>
                    </div>
                </div>
                <div class="project-team">
                    <span class="team-label">الفريق:</span>
                    <div class="team-avatars">
                        <div class="team-avatar">مح</div>
                        <div class="team-avatar">نو</div>
                        <div class="team-avatar">عل</div>
                    </div>
                </div>
            </div>
            <div class="project-footer">
                <div class="project-deadline">
                    <i class="fas fa-calendar"></i>
                    الموعد النهائي: 30 نوفمبر 2025
                </div>
                <div class="project-actions">
                    <button class="btn-project btn-view">
                        <i class="fas fa-eye"></i>
                        عرض
                    </button>
                    <button class="btn-project btn-edit">
                        <i class="fas fa-edit"></i>
                        تعديل
                    </button>
                </div>
            </div>
        </div>

        <!-- مشروع منخفض الأولوية -->
        <div class="project-card low-priority">
            <div class="project-header">
                <h3 class="project-title">تطوير تطبيق الهاتف المحمول</h3>
                <p class="project-description">
                    إنشاء تطبيق للهواتف الذكية لتسهيل الوصول إلى النظام وتحسين تجربة المستخدم.
                </p>
                <div class="project-meta">
                    <div class="project-status status-pending">
                        <i class="fas fa-clock"></i>
                        في الانتظار
                    </div>
                    <div class="project-priority">
                        <i class="fas fa-arrow-down"></i>
                        أولوية منخفضة
                    </div>
                </div>
                <div class="project-progress">
                    <div class="progress-header">
                        <span class="progress-label">التقدم</span>
                        <span class="progress-percentage">15%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 15%;"></div>
                    </div>
                </div>
                <div class="project-team">
                    <span class="team-label">الفريق:</span>
                    <div class="team-avatars">
                        <div class="team-avatar">خا</div>
                        <div class="team-avatar">يا</div>
                    </div>
                </div>
            </div>
            <div class="project-footer">
                <div class="project-deadline">
                    <i class="fas fa-calendar"></i>
                    الموعد النهائي: 31 ديسمبر 2025
                </div>
                <div class="project-actions">
                    <button class="btn-project btn-view">
                        <i class="fas fa-eye"></i>
                        عرض
                    </button>
                    <button class="btn-project btn-edit">
                        <i class="fas fa-edit"></i>
                        تعديل
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- زر إضافة مشروع جديد -->
    <button class="add-project-btn" onclick="showAddProjectModal()">
        <i class="fas fa-plus"></i>
    </button>
</div>

<script>
// تحديث أشرطة التقدم عند تحميل الصفحة
window.addEventListener('load', function() {
    const progressBars = document.querySelectorAll('.progress-fill');
    progressBars.forEach(bar => {
        const width = bar.style.width;
        bar.style.width = '0%';
        setTimeout(() => {
            bar.style.width = width;
        }, 500);
    });
});

// دالة إضافة مشروع جديد
function showAddProjectModal() {
    alert('سيتم إضافة نافذة إضافة مشروع جديد');
}

// تفعيل التفاعلات مع البطاقات
document.querySelectorAll('.project-card').forEach(card => {
    card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-8px) scale(1.02)';
    });
    
    card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
    });
});
</script>
{% endblock %}
