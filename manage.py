#!/usr/bin/env python
"""
أداة إدارة نظام ALEMIS
ALEMIS System Management Tool
"""
import os
import sys
import click
from datetime import datetime, date, timedelta
from flask.cli import with_appcontext
from app import create_app
from app.database import db_manager
from app.models import User, Department, LeaveType, LeaveBalance, UserRole
from app.security import password_manager
from app.reports import report_generator, ReportType, ReportFilter, ReportFormat


# إنشاء التطبيق
app = create_app()


@click.group()
def cli():
    """أداة إدارة نظام ALEMIS"""
    pass


@cli.command()
@with_appcontext
def init_db():
    """تهيئة قاعدة البيانات"""
    click.echo("🔄 تهيئة قاعدة البيانات...")
    
    try:
        db_manager.create_all()
        click.echo("✅ تم إنشاء جداول قاعدة البيانات بنجاح")
        
        # إنشاء البيانات الافتراضية
        create_default_data()
        click.echo("✅ تم إنشاء البيانات الافتراضية بنجاح")
        
    except Exception as e:
        click.echo(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
        sys.exit(1)


@cli.command()
@with_appcontext
def reset_db():
    """إعادة تعيين قاعدة البيانات"""
    if click.confirm("⚠️ هل أنت متأكد من إعادة تعيين قاعدة البيانات؟ سيتم حذف جميع البيانات!"):
        try:
            db_manager.drop_all()
            db_manager.create_all()
            create_default_data()
            click.echo("✅ تم إعادة تعيين قاعدة البيانات بنجاح")
        except Exception as e:
            click.echo(f"❌ خطأ في إعادة تعيين قاعدة البيانات: {e}")
            sys.exit(1)


@cli.command()
@click.option('--username', prompt='اسم المستخدم', help='اسم المستخدم')
@click.option('--email', prompt='البريد الإلكتروني', help='البريد الإلكتروني')
@click.option('--password', prompt='كلمة المرور', hide_input=True, help='كلمة المرور')
@click.option('--first-name', prompt='الاسم الأول', help='الاسم الأول')
@click.option('--last-name', prompt='الاسم الأخير', help='الاسم الأخير')
@click.option('--role', type=click.Choice(['admin', 'hr', 'manager', 'gm', 'employee']), 
              prompt='الدور', help='دور المستخدم')
@with_appcontext
def create_user(username, email, password, first_name, last_name, role):
    """إنشاء مستخدم جديد"""
    try:
        with db_manager.session_scope() as session:
            # التحقق من عدم وجود المستخدم
            existing_user = session.query(User).filter(
                (User.username == username) | (User.email == email)
            ).first()
            
            if existing_user:
                click.echo("❌ المستخدم موجود بالفعل")
                return
            
            # الحصول على القسم الافتراضي
            department = session.query(Department).first()
            if not department:
                click.echo("❌ لا يوجد قسم متاح. يرجى إنشاء قسم أولاً")
                return
            
            # إنشاء المستخدم
            user = User(
                username=username,
                email=email,
                first_name=first_name,
                last_name=last_name,
                role=UserRole(role),
                department_id=department.id,
                is_active=True,
                is_verified=True
            )
            user.set_password(password)
            
            session.add(user)
            session.flush()
            
            click.echo(f"✅ تم إنشاء المستخدم {username} بنجاح")
            
    except Exception as e:
        click.echo(f"❌ خطأ في إنشاء المستخدم: {e}")


@cli.command()
@click.option('--username', prompt='اسم المستخدم', help='اسم المستخدم')
@click.option('--password', prompt='كلمة المرور الجديدة', hide_input=True, help='كلمة المرور الجديدة')
@with_appcontext
def change_password(username, password):
    """تغيير كلمة مرور المستخدم"""
    try:
        with db_manager.session_scope() as session:
            user = session.query(User).filter(User.username == username).first()
            
            if not user:
                click.echo("❌ المستخدم غير موجود")
                return
            
            # التحقق من قوة كلمة المرور
            is_strong, message = password_manager.validate_password_strength(password)
            if not is_strong:
                click.echo(f"❌ {message}")
                return
            
            user.set_password(password)
            session.flush()
            
            click.echo(f"✅ تم تغيير كلمة مرور المستخدم {username} بنجاح")
            
    except Exception as e:
        click.echo(f"❌ خطأ في تغيير كلمة المرور: {e}")


@cli.command()
@with_appcontext
def list_users():
    """عرض قائمة المستخدمين"""
    try:
        with db_manager.session_scope() as session:
            users = session.query(User).all()
            
            if not users:
                click.echo("لا يوجد مستخدمين")
                return
            
            click.echo("\n📋 قائمة المستخدمين:")
            click.echo("-" * 80)
            click.echo(f"{'ID':<5} {'اسم المستخدم':<15} {'البريد الإلكتروني':<25} {'الدور':<10} {'نشط':<5}")
            click.echo("-" * 80)
            
            for user in users:
                status = "✅" if user.is_active else "❌"
                click.echo(f"{user.id:<5} {user.username:<15} {user.email:<25} {user.role.value:<10} {status:<5}")
            
            click.echo("-" * 80)
            click.echo(f"إجمالي المستخدمين: {len(users)}")
            
    except Exception as e:
        click.echo(f"❌ خطأ في عرض المستخدمين: {e}")


@cli.command()
@click.option('--type', 'report_type', type=click.Choice(['leave_summary', 'employee_report', 'monthly_report']),
              prompt='نوع التقرير', help='نوع التقرير')
@click.option('--format', 'report_format', type=click.Choice(['pdf', 'excel', 'csv']),
              default='pdf', help='تنسيق التقرير')
@click.option('--start-date', help='تاريخ البداية (YYYY-MM-DD)')
@click.option('--end-date', help='تاريخ النهاية (YYYY-MM-DD)')
@click.option('--output', help='مسار ملف الإخراج')
@with_appcontext
def generate_report(report_type, report_format, start_date, end_date, output):
    """إنتاج تقرير"""
    try:
        # تحويل التواريخ
        start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date() if start_date else None
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date() if end_date else None
        
        # إنشاء المرشح
        filters = ReportFilter(
            start_date=start_date_obj,
            end_date=end_date_obj
        )
        
        click.echo(f"🔄 إنتاج تقرير {report_type} بتنسيق {report_format}...")
        
        # إنتاج التقرير
        report_data = report_generator.generate(
            report_type=ReportType(report_type),
            filters=filters,
            format=ReportFormat(report_format)
        )
        
        # حفظ التقرير
        if not output:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output = f"report_{report_type}_{timestamp}.{report_format}"
        
        with open(output, 'wb') as f:
            f.write(report_data)
        
        click.echo(f"✅ تم إنتاج التقرير وحفظه في: {output}")
        
    except Exception as e:
        click.echo(f"❌ خطأ في إنتاج التقرير: {e}")


@cli.command()
@with_appcontext
def backup_db():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    try:
        from app.tasks import create_backup
        
        click.echo("🔄 إنشاء نسخة احتياطية...")
        result = create_backup()
        
        if result['status'] == 'success':
            click.echo(f"✅ تم إنشاء النسخة الاحتياطية: {result['backup_file']}")
            click.echo(f"📊 حجم الملف: {result['size']} بايت")
        else:
            click.echo("❌ فشل في إنشاء النسخة الاحتياطية")
            
    except Exception as e:
        click.echo(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")


@cli.command()
@with_appcontext
def cleanup_data():
    """تنظيف البيانات القديمة"""
    try:
        from app.tasks import cleanup_old_data
        
        click.echo("🔄 تنظيف البيانات القديمة...")
        result = cleanup_old_data()
        
        if result['status'] == 'success':
            click.echo(f"✅ تم حذف {result['deleted_audit_logs']} سجل تدقيق قديم")
            click.echo(f"✅ تم حذف {result['deleted_notifications']} إشعار قديم")
        else:
            click.echo("❌ فشل في تنظيف البيانات")
            
    except Exception as e:
        click.echo(f"❌ خطأ في تنظيف البيانات: {e}")


@cli.command()
@with_appcontext
def update_balances():
    """تحديث أرصدة الإجازات"""
    try:
        from app.tasks import update_leave_balances
        
        click.echo("🔄 تحديث أرصدة الإجازات...")
        result = update_leave_balances()
        
        if result['status'] == 'success':
            click.echo(f"✅ تم تحديث أرصدة {result['updated_users']} موظف")
        else:
            click.echo("❌ فشل في تحديث الأرصدة")
            
    except Exception as e:
        click.echo(f"❌ خطأ في تحديث الأرصدة: {e}")


@cli.command()
@with_appcontext
def health_check():
    """فحص صحة النظام"""
    try:
        from app.monitoring import health_checker
        
        click.echo("🔄 فحص صحة النظام...")
        results = health_checker.run_checks()
        
        click.echo(f"\n📊 حالة النظام: {results['status']}")
        click.echo(f"🕐 وقت الفحص: {results['timestamp']}")
        click.echo("\n📋 تفاصيل الفحوصات:")
        click.echo("-" * 50)
        
        for check_name, check_result in results['checks'].items():
            status = "✅" if check_result['healthy'] else "❌"
            message = check_result.get('message', check_result.get('error', ''))
            click.echo(f"{status} {check_name}: {message}")
        
    except Exception as e:
        click.echo(f"❌ خطأ في فحص صحة النظام: {e}")


def create_default_data():
    """إنشاء البيانات الافتراضية"""
    with db_manager.session_scope() as session:
        # التحقق من وجود البيانات
        if session.query(User).count() > 0:
            return
        
        # إنشاء الأقسام الافتراضية
        departments_data = [
            ("قسم المختبر", "قسم التحاليل الطبية والفحوصات المخبرية"),
            ("قسم الأشعة", "قسم التصوير الطبي والأشعة التشخيصية")
        ]

        departments = []
        for name, description in departments_data:
            department = Department(
                name=name,
                description=description,
                is_active=True
            )
            session.add(department)
            departments.append(department)
        session.add(department)
        session.flush()
        
        # إنشاء أنواع الإجازات
        leave_types = [
            LeaveType(name="إجازة اعتيادية", description="الإجازة السنوية العادية", default_days=30),
            LeaveType(name="إجازة مرضية", description="إجازة للمرض", default_days=15),
            LeaveType(name="إجازة طارئة", description="إجازة للحالات الطارئة", default_days=5),
            LeaveType(name="إجازة أمومة", description="إجازة الأمومة", default_days=90),
            LeaveType(name="إجازة حج", description="إجازة لأداء فريضة الحج", default_days=21),
        ]
        
        for leave_type in leave_types:
            session.add(leave_type)
        
        session.flush()
        
        # إنشاء المستخدمين الافتراضيين
        users_data = [
            ("admin", "<EMAIL>", "مدير", "النظام", UserRole.ADMIN, 0),  # قسم المختبر
            ("hr", "<EMAIL>", "مدير", "الموارد البشرية", UserRole.HR, 0),  # قسم المختبر
            ("gm", "<EMAIL>", "المدير", "العام", UserRole.GM, 0),  # قسم المختبر
            ("lab_manager", "<EMAIL>", "مدير", "المختبر", UserRole.MANAGER, 0),  # قسم المختبر
            ("rad_manager", "<EMAIL>", "مدير", "الأشعة", UserRole.MANAGER, 1),  # قسم الأشعة
            ("lab_emp1", "<EMAIL>", "فني", "مختبر أول", UserRole.EMPLOYEE, 0),  # قسم المختبر
            ("lab_emp2", "<EMAIL>", "فني", "مختبر ثاني", UserRole.EMPLOYEE, 0),  # قسم المختبر
            ("rad_emp1", "<EMAIL>", "فني", "أشعة أول", UserRole.EMPLOYEE, 1),  # قسم الأشعة
            ("rad_emp2", "<EMAIL>", "فني", "أشعة ثاني", UserRole.EMPLOYEE, 1),  # قسم الأشعة
        ]

        for username, email, first_name, last_name, role, dept_index in users_data:
            user = User(
                username=username,
                email=email,
                first_name=first_name,
                last_name=last_name,
                role=role,
                department_id=departments[dept_index].id,
                is_active=True,
                is_verified=True
            )
            user.set_password("admin123")
            session.add(user)
        
        session.commit()


if __name__ == '__main__':
    with app.app_context():
        cli()
