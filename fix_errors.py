#!/usr/bin/env python3
"""
سكريبت لإصلاح الأخطاء في app.py
"""

import re

def fix_user_access_errors():
    """إصلاح أخطاء الوصول للمستخدم"""
    
    # قراءة الملف
    with open('app.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # إصلاح user.get('role') إلى user['role']
    content = re.sub(r"user\.get\('role'\)", "user['role']", content)
    content = re.sub(r"user\.get\('id'\)", "user['id']", content)
    
    # إصلاح result.get('count', 0) إلى result['count']
    content = re.sub(r"result\.get\('count', 0\)", "result['count']", content)
    
    # كتابة الملف المحدث
    with open('app.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ تم إصلاح أخطاء الوصول للمستخدم")

if __name__ == "__main__":
    fix_user_access_errors()
